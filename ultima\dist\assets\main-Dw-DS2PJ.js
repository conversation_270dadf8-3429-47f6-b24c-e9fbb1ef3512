function ng(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var kx=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Id(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var zd={exports:{}},qi={},Md={exports:{}},Q={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bo=Symbol.for("react.element"),rg=Symbol.for("react.portal"),og=Symbol.for("react.fragment"),ig=Symbol.for("react.strict_mode"),lg=Symbol.for("react.profiler"),sg=Symbol.for("react.provider"),ag=Symbol.for("react.context"),ug=Symbol.for("react.forward_ref"),cg=Symbol.for("react.suspense"),dg=Symbol.for("react.memo"),fg=Symbol.for("react.lazy"),Vu=Symbol.iterator;function pg(e){return e===null||typeof e!="object"?null:(e=Vu&&e[Vu]||e["@@iterator"],typeof e=="function"?e:null)}var Fd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},jd=Object.assign,Ud={};function xr(e,t,n){this.props=e,this.context=t,this.refs=Ud,this.updater=n||Fd}xr.prototype.isReactComponent={};xr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};xr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Bd(){}Bd.prototype=xr.prototype;function ba(e,t,n){this.props=e,this.context=t,this.refs=Ud,this.updater=n||Fd}var Pa=ba.prototype=new Bd;Pa.constructor=ba;jd(Pa,xr.prototype);Pa.isPureReactComponent=!0;var Hu=Array.isArray,$d=Object.prototype.hasOwnProperty,Ra={current:null},Vd={key:!0,ref:!0,__self:!0,__source:!0};function Hd(e,t,n){var r,o={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)$d.call(t,r)&&!Vd.hasOwnProperty(r)&&(o[r]=t[r]);var s=arguments.length-2;if(s===1)o.children=n;else if(1<s){for(var a=Array(s),u=0;u<s;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)o[r]===void 0&&(o[r]=s[r]);return{$$typeof:bo,type:e,key:i,ref:l,props:o,_owner:Ra.current}}function hg(e,t){return{$$typeof:bo,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Na(e){return typeof e=="object"&&e!==null&&e.$$typeof===bo}function mg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Wu=/\/+/g;function Rl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?mg(""+e.key):t.toString(36)}function ii(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case bo:case rg:l=!0}}if(l)return l=e,o=o(l),e=r===""?"."+Rl(l,0):r,Hu(o)?(n="",e!=null&&(n=e.replace(Wu,"$&/")+"/"),ii(o,t,n,"",function(u){return u})):o!=null&&(Na(o)&&(o=hg(o,n+(!o.key||l&&l.key===o.key?"":(""+o.key).replace(Wu,"$&/")+"/")+e)),t.push(o)),1;if(l=0,r=r===""?".":r+":",Hu(e))for(var s=0;s<e.length;s++){i=e[s];var a=r+Rl(i,s);l+=ii(i,t,n,a,o)}else if(a=pg(e),typeof a=="function")for(e=a.call(e),s=0;!(i=e.next()).done;)i=i.value,a=r+Rl(i,s++),l+=ii(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function Io(e,t,n){if(e==null)return e;var r=[],o=0;return ii(e,r,"","",function(i){return t.call(n,i,o++)}),r}function gg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Oe={current:null},li={transition:null},vg={ReactCurrentDispatcher:Oe,ReactCurrentBatchConfig:li,ReactCurrentOwner:Ra};function Wd(){throw Error("act(...) is not supported in production builds of React.")}Q.Children={map:Io,forEach:function(e,t,n){Io(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Io(e,function(){t++}),t},toArray:function(e){return Io(e,function(t){return t})||[]},only:function(e){if(!Na(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Q.Component=xr;Q.Fragment=og;Q.Profiler=lg;Q.PureComponent=ba;Q.StrictMode=ig;Q.Suspense=cg;Q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=vg;Q.act=Wd;Q.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=jd({},e.props),o=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=Ra.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(a in t)$d.call(t,a)&&!Vd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&s!==void 0?s[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){s=Array(a);for(var u=0;u<a;u++)s[u]=arguments[u+2];r.children=s}return{$$typeof:bo,type:e.type,key:o,ref:i,props:r,_owner:l}};Q.createContext=function(e){return e={$$typeof:ag,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:sg,_context:e},e.Consumer=e};Q.createElement=Hd;Q.createFactory=function(e){var t=Hd.bind(null,e);return t.type=e,t};Q.createRef=function(){return{current:null}};Q.forwardRef=function(e){return{$$typeof:ug,render:e}};Q.isValidElement=Na;Q.lazy=function(e){return{$$typeof:fg,_payload:{_status:-1,_result:e},_init:gg}};Q.memo=function(e,t){return{$$typeof:dg,type:e,compare:t===void 0?null:t}};Q.startTransition=function(e){var t=li.transition;li.transition={};try{e()}finally{li.transition=t}};Q.unstable_act=Wd;Q.useCallback=function(e,t){return Oe.current.useCallback(e,t)};Q.useContext=function(e){return Oe.current.useContext(e)};Q.useDebugValue=function(){};Q.useDeferredValue=function(e){return Oe.current.useDeferredValue(e)};Q.useEffect=function(e,t){return Oe.current.useEffect(e,t)};Q.useId=function(){return Oe.current.useId()};Q.useImperativeHandle=function(e,t,n){return Oe.current.useImperativeHandle(e,t,n)};Q.useInsertionEffect=function(e,t){return Oe.current.useInsertionEffect(e,t)};Q.useLayoutEffect=function(e,t){return Oe.current.useLayoutEffect(e,t)};Q.useMemo=function(e,t){return Oe.current.useMemo(e,t)};Q.useReducer=function(e,t,n){return Oe.current.useReducer(e,t,n)};Q.useRef=function(e){return Oe.current.useRef(e)};Q.useState=function(e){return Oe.current.useState(e)};Q.useSyncExternalStore=function(e,t,n){return Oe.current.useSyncExternalStore(e,t,n)};Q.useTransition=function(){return Oe.current.useTransition()};Q.version="18.3.1";Md.exports=Q;var g=Md.exports;const Nt=Id(g),yg=ng({__proto__:null,default:Nt},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wg=g,Sg=Symbol.for("react.element"),xg=Symbol.for("react.fragment"),_g=Object.prototype.hasOwnProperty,Cg=wg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Eg={key:!0,ref:!0,__self:!0,__source:!0};function Kd(e,t,n){var r,o={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)_g.call(t,r)&&!Eg.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Sg,type:e,key:i,ref:l,props:o,_owner:Cg.current}}qi.Fragment=xg;qi.jsx=Kd;qi.jsxs=Kd;zd.exports=qi;var N=zd.exports,Ku={},Gd={exports:{}},Ye={},Qd={exports:{}},Yd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(b,I){var F=b.length;b.push(I);e:for(;0<F;){var B=F-1>>>1,q=b[B];if(0<o(q,I))b[B]=I,b[F]=q,F=B;else break e}}function n(b){return b.length===0?null:b[0]}function r(b){if(b.length===0)return null;var I=b[0],F=b.pop();if(F!==I){b[0]=F;e:for(var B=0,q=b.length,Ee=q>>>1;B<Ee;){var ve=2*(B+1)-1,Ze=b[ve],he=ve+1,V=b[he];if(0>o(Ze,F))he<q&&0>o(V,Ze)?(b[B]=V,b[he]=F,B=he):(b[B]=Ze,b[ve]=F,B=ve);else if(he<q&&0>o(V,F))b[B]=V,b[he]=F,B=he;else break e}}return I}function o(b,I){var F=b.sortIndex-I.sortIndex;return F!==0?F:b.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,s=l.now();e.unstable_now=function(){return l.now()-s}}var a=[],u=[],d=1,m=null,h=3,y=!1,w=!1,p=!1,S=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(b){for(var I=n(u);I!==null;){if(I.callback===null)r(u);else if(I.startTime<=b)r(u),I.sortIndex=I.expirationTime,t(a,I);else break;I=n(u)}}function x(b){if(p=!1,v(b),!w)if(n(a)!==null)w=!0,j(C);else{var I=n(u);I!==null&&$(x,I.startTime-b)}}function C(b,I){w=!1,p&&(p=!1,f(P),P=-1),y=!0;var F=h;try{for(v(I),m=n(a);m!==null&&(!(m.expirationTime>I)||b&&!O());){var B=m.callback;if(typeof B=="function"){m.callback=null,h=m.priorityLevel;var q=B(m.expirationTime<=I);I=e.unstable_now(),typeof q=="function"?m.callback=q:m===n(a)&&r(a),v(I)}else r(a);m=n(a)}if(m!==null)var Ee=!0;else{var ve=n(u);ve!==null&&$(x,ve.startTime-I),Ee=!1}return Ee}finally{m=null,h=F,y=!1}}var A=!1,_=null,P=-1,z=5,E=-1;function O(){return!(e.unstable_now()-E<z)}function R(){if(_!==null){var b=e.unstable_now();E=b;var I=!0;try{I=_(!0,b)}finally{I?D():(A=!1,_=null)}}else A=!1}var D;if(typeof c=="function")D=function(){c(R)};else if(typeof MessageChannel<"u"){var k=new MessageChannel,U=k.port2;k.port1.onmessage=R,D=function(){U.postMessage(null)}}else D=function(){S(R,0)};function j(b){_=b,A||(A=!0,D())}function $(b,I){P=S(function(){b(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(b){b.callback=null},e.unstable_continueExecution=function(){w||y||(w=!0,j(C))},e.unstable_forceFrameRate=function(b){0>b||125<b?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):z=0<b?Math.floor(1e3/b):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(b){switch(h){case 1:case 2:case 3:var I=3;break;default:I=h}var F=h;h=I;try{return b()}finally{h=F}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(b,I){switch(b){case 1:case 2:case 3:case 4:case 5:break;default:b=3}var F=h;h=b;try{return I()}finally{h=F}},e.unstable_scheduleCallback=function(b,I,F){var B=e.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?B+F:B):F=B,b){case 1:var q=-1;break;case 2:q=250;break;case 5:q=**********;break;case 4:q=1e4;break;default:q=5e3}return q=F+q,b={id:d++,callback:I,priorityLevel:b,startTime:F,expirationTime:q,sortIndex:-1},F>B?(b.sortIndex=F,t(u,b),n(a)===null&&b===n(u)&&(p?(f(P),P=-1):p=!0,$(x,F-B))):(b.sortIndex=q,t(a,b),w||y||(w=!0,j(C))),b},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(b){var I=h;return function(){var F=h;h=I;try{return b.apply(this,arguments)}finally{h=F}}}})(Yd);Qd.exports=Yd;var kg=Qd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bg=g,Qe=kg;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Xd=new Set,lo={};function Ln(e,t){dr(e,t),dr(e+"Capture",t)}function dr(e,t){for(lo[e]=t,e=0;e<t.length;e++)Xd.add(t[e])}var It=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vs=Object.prototype.hasOwnProperty,Pg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Gu={},Qu={};function Rg(e){return vs.call(Qu,e)?!0:vs.call(Gu,e)?!1:Pg.test(e)?Qu[e]=!0:(Gu[e]=!0,!1)}function Ng(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ag(e,t,n,r){if(t===null||typeof t>"u"||Ng(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Le(e,t,n,r,o,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new Le(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new Le(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new Le(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new Le(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new Le(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new Le(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new Le(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new Le(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new Le(e,5,!1,e.toLowerCase(),null,!1,!1)});var Aa=/[\-:]([a-z])/g;function Ta(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Aa,Ta);Ce[t]=new Le(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Aa,Ta);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Aa,Ta);Ce[t]=new Le(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new Le("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new Le(e,1,!1,e.toLowerCase(),null,!0,!0)});function Oa(e,t,n,r){var o=Ce.hasOwnProperty(t)?Ce[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ag(t,n,o,r)&&(n=null),r||o===null?Rg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var $t=bg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,zo=Symbol.for("react.element"),Bn=Symbol.for("react.portal"),$n=Symbol.for("react.fragment"),La=Symbol.for("react.strict_mode"),ys=Symbol.for("react.profiler"),Zd=Symbol.for("react.provider"),Jd=Symbol.for("react.context"),Da=Symbol.for("react.forward_ref"),ws=Symbol.for("react.suspense"),Ss=Symbol.for("react.suspense_list"),Ia=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),qd=Symbol.for("react.offscreen"),Yu=Symbol.iterator;function Or(e){return e===null||typeof e!="object"?null:(e=Yu&&e[Yu]||e["@@iterator"],typeof e=="function"?e:null)}var ae=Object.assign,Nl;function Vr(e){if(Nl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Nl=t&&t[1]||""}return`
`+Nl+e}var Al=!1;function Tl(e,t){if(!e||Al)return"";Al=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),l=o.length-1,s=i.length-1;1<=l&&0<=s&&o[l]!==i[s];)s--;for(;1<=l&&0<=s;l--,s--)if(o[l]!==i[s]){if(l!==1||s!==1)do if(l--,s--,0>s||o[l]!==i[s]){var a=`
`+o[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=s);break}}}finally{Al=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Vr(e):""}function Tg(e){switch(e.tag){case 5:return Vr(e.type);case 16:return Vr("Lazy");case 13:return Vr("Suspense");case 19:return Vr("SuspenseList");case 0:case 2:case 15:return e=Tl(e.type,!1),e;case 11:return e=Tl(e.type.render,!1),e;case 1:return e=Tl(e.type,!0),e;default:return""}}function xs(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case $n:return"Fragment";case Bn:return"Portal";case ys:return"Profiler";case La:return"StrictMode";case ws:return"Suspense";case Ss:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Jd:return(e.displayName||"Context")+".Consumer";case Zd:return(e._context.displayName||"Context")+".Provider";case Da:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ia:return t=e.displayName||null,t!==null?t:xs(e.type)||"Memo";case Gt:t=e._payload,e=e._init;try{return xs(e(t))}catch{}}return null}function Og(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xs(t);case 8:return t===La?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function un(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ef(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Lg(e){var t=ef(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Mo(e){e._valueTracker||(e._valueTracker=Lg(e))}function tf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ef(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Ci(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function _s(e,t){var n=t.checked;return ae({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Xu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=un(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function nf(e,t){t=t.checked,t!=null&&Oa(e,"checked",t,!1)}function Cs(e,t){nf(e,t);var n=un(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Es(e,t.type,n):t.hasOwnProperty("defaultValue")&&Es(e,t.type,un(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Zu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Es(e,t,n){(t!=="number"||Ci(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Hr=Array.isArray;function tr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+un(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function ks(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return ae({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ju(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if(Hr(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:un(n)}}function rf(e,t){var n=un(t.value),r=un(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function qu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function of(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function bs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?of(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Fo,lf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Fo=Fo||document.createElement("div"),Fo.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Fo.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function so(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Qr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Dg=["Webkit","ms","Moz","O"];Object.keys(Qr).forEach(function(e){Dg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Qr[t]=Qr[e]})});function sf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Qr.hasOwnProperty(e)&&Qr[e]?(""+t).trim():t+"px"}function af(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=sf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var Ig=ae({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ps(e,t){if(t){if(Ig[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function Rs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ns=null;function za(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var As=null,nr=null,rr=null;function ec(e){if(e=No(e)){if(typeof As!="function")throw Error(L(280));var t=e.stateNode;t&&(t=ol(t),As(e.stateNode,e.type,t))}}function uf(e){nr?rr?rr.push(e):rr=[e]:nr=e}function cf(){if(nr){var e=nr,t=rr;if(rr=nr=null,ec(e),t)for(e=0;e<t.length;e++)ec(t[e])}}function df(e,t){return e(t)}function ff(){}var Ol=!1;function pf(e,t,n){if(Ol)return e(t,n);Ol=!0;try{return df(e,t,n)}finally{Ol=!1,(nr!==null||rr!==null)&&(ff(),cf())}}function ao(e,t){var n=e.stateNode;if(n===null)return null;var r=ol(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var Ts=!1;if(It)try{var Lr={};Object.defineProperty(Lr,"passive",{get:function(){Ts=!0}}),window.addEventListener("test",Lr,Lr),window.removeEventListener("test",Lr,Lr)}catch{Ts=!1}function zg(e,t,n,r,o,i,l,s,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var Yr=!1,Ei=null,ki=!1,Os=null,Mg={onError:function(e){Yr=!0,Ei=e}};function Fg(e,t,n,r,o,i,l,s,a){Yr=!1,Ei=null,zg.apply(Mg,arguments)}function jg(e,t,n,r,o,i,l,s,a){if(Fg.apply(this,arguments),Yr){if(Yr){var u=Ei;Yr=!1,Ei=null}else throw Error(L(198));ki||(ki=!0,Os=u)}}function Dn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function hf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function tc(e){if(Dn(e)!==e)throw Error(L(188))}function Ug(e){var t=e.alternate;if(!t){if(t=Dn(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return tc(o),e;if(i===r)return tc(o),t;i=i.sibling}throw Error(L(188))}if(n.return!==r.return)n=o,r=i;else{for(var l=!1,s=o.child;s;){if(s===n){l=!0,n=o,r=i;break}if(s===r){l=!0,r=o,n=i;break}s=s.sibling}if(!l){for(s=i.child;s;){if(s===n){l=!0,n=i,r=o;break}if(s===r){l=!0,r=i,n=o;break}s=s.sibling}if(!l)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function mf(e){return e=Ug(e),e!==null?gf(e):null}function gf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=gf(e);if(t!==null)return t;e=e.sibling}return null}var vf=Qe.unstable_scheduleCallback,nc=Qe.unstable_cancelCallback,Bg=Qe.unstable_shouldYield,$g=Qe.unstable_requestPaint,ce=Qe.unstable_now,Vg=Qe.unstable_getCurrentPriorityLevel,Ma=Qe.unstable_ImmediatePriority,yf=Qe.unstable_UserBlockingPriority,bi=Qe.unstable_NormalPriority,Hg=Qe.unstable_LowPriority,wf=Qe.unstable_IdlePriority,el=null,_t=null;function Wg(e){if(_t&&typeof _t.onCommitFiberRoot=="function")try{_t.onCommitFiberRoot(el,e,void 0,(e.current.flags&128)===128)}catch{}}var ft=Math.clz32?Math.clz32:Qg,Kg=Math.log,Gg=Math.LN2;function Qg(e){return e>>>=0,e===0?32:31-(Kg(e)/Gg|0)|0}var jo=64,Uo=4194304;function Wr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Pi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var s=l&~o;s!==0?r=Wr(s):(i&=l,i!==0&&(r=Wr(i)))}else l=n&~o,l!==0?r=Wr(l):i!==0&&(r=Wr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ft(t),o=1<<n,r|=e[n],t&=~o;return r}function Yg(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Xg(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-ft(i),s=1<<l,a=o[l];a===-1?(!(s&n)||s&r)&&(o[l]=Yg(s,t)):a<=t&&(e.expiredLanes|=s),i&=~s}}function Ls(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Sf(){var e=jo;return jo<<=1,!(jo&4194240)&&(jo=64),e}function Ll(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Po(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ft(t),e[t]=n}function Zg(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-ft(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Fa(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ft(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var Z=0;function xf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var _f,ja,Cf,Ef,kf,Ds=!1,Bo=[],en=null,tn=null,nn=null,uo=new Map,co=new Map,Xt=[],Jg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function rc(e,t){switch(e){case"focusin":case"focusout":en=null;break;case"dragenter":case"dragleave":tn=null;break;case"mouseover":case"mouseout":nn=null;break;case"pointerover":case"pointerout":uo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":co.delete(t.pointerId)}}function Dr(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=No(t),t!==null&&ja(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function qg(e,t,n,r,o){switch(t){case"focusin":return en=Dr(en,e,t,n,r,o),!0;case"dragenter":return tn=Dr(tn,e,t,n,r,o),!0;case"mouseover":return nn=Dr(nn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return uo.set(i,Dr(uo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,co.set(i,Dr(co.get(i)||null,e,t,n,r,o)),!0}return!1}function bf(e){var t=xn(e.target);if(t!==null){var n=Dn(t);if(n!==null){if(t=n.tag,t===13){if(t=hf(n),t!==null){e.blockedOn=t,kf(e.priority,function(){Cf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function si(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Is(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ns=r,n.target.dispatchEvent(r),Ns=null}else return t=No(n),t!==null&&ja(t),e.blockedOn=n,!1;t.shift()}return!0}function oc(e,t,n){si(e)&&n.delete(t)}function ev(){Ds=!1,en!==null&&si(en)&&(en=null),tn!==null&&si(tn)&&(tn=null),nn!==null&&si(nn)&&(nn=null),uo.forEach(oc),co.forEach(oc)}function Ir(e,t){e.blockedOn===t&&(e.blockedOn=null,Ds||(Ds=!0,Qe.unstable_scheduleCallback(Qe.unstable_NormalPriority,ev)))}function fo(e){function t(o){return Ir(o,e)}if(0<Bo.length){Ir(Bo[0],e);for(var n=1;n<Bo.length;n++){var r=Bo[n];r.blockedOn===e&&(r.blockedOn=null)}}for(en!==null&&Ir(en,e),tn!==null&&Ir(tn,e),nn!==null&&Ir(nn,e),uo.forEach(t),co.forEach(t),n=0;n<Xt.length;n++)r=Xt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Xt.length&&(n=Xt[0],n.blockedOn===null);)bf(n),n.blockedOn===null&&Xt.shift()}var or=$t.ReactCurrentBatchConfig,Ri=!0;function tv(e,t,n,r){var o=Z,i=or.transition;or.transition=null;try{Z=1,Ua(e,t,n,r)}finally{Z=o,or.transition=i}}function nv(e,t,n,r){var o=Z,i=or.transition;or.transition=null;try{Z=4,Ua(e,t,n,r)}finally{Z=o,or.transition=i}}function Ua(e,t,n,r){if(Ri){var o=Is(e,t,n,r);if(o===null)Vl(e,t,r,Ni,n),rc(e,r);else if(qg(o,e,t,n,r))r.stopPropagation();else if(rc(e,r),t&4&&-1<Jg.indexOf(e)){for(;o!==null;){var i=No(o);if(i!==null&&_f(i),i=Is(e,t,n,r),i===null&&Vl(e,t,r,Ni,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Vl(e,t,r,null,n)}}var Ni=null;function Is(e,t,n,r){if(Ni=null,e=za(r),e=xn(e),e!==null)if(t=Dn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=hf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ni=e,null}function Pf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vg()){case Ma:return 1;case yf:return 4;case bi:case Hg:return 16;case wf:return 536870912;default:return 16}default:return 16}}var Jt=null,Ba=null,ai=null;function Rf(){if(ai)return ai;var e,t=Ba,n=t.length,r,o="value"in Jt?Jt.value:Jt.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===o[i-r];r++);return ai=o.slice(e,1<r?1-r:void 0)}function ui(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function $o(){return!0}function ic(){return!1}function Xe(e){function t(n,r,o,i,l){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?$o:ic,this.isPropagationStopped=ic,this}return ae(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=$o)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=$o)},persist:function(){},isPersistent:$o}),t}var _r={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},$a=Xe(_r),Ro=ae({},_r,{view:0,detail:0}),rv=Xe(Ro),Dl,Il,zr,tl=ae({},Ro,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Va,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==zr&&(zr&&e.type==="mousemove"?(Dl=e.screenX-zr.screenX,Il=e.screenY-zr.screenY):Il=Dl=0,zr=e),Dl)},movementY:function(e){return"movementY"in e?e.movementY:Il}}),lc=Xe(tl),ov=ae({},tl,{dataTransfer:0}),iv=Xe(ov),lv=ae({},Ro,{relatedTarget:0}),zl=Xe(lv),sv=ae({},_r,{animationName:0,elapsedTime:0,pseudoElement:0}),av=Xe(sv),uv=ae({},_r,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),cv=Xe(uv),dv=ae({},_r,{data:0}),sc=Xe(dv),fv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},pv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=hv[e])?!!t[e]:!1}function Va(){return mv}var gv=ae({},Ro,{key:function(e){if(e.key){var t=fv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ui(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?pv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Va,charCode:function(e){return e.type==="keypress"?ui(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ui(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),vv=Xe(gv),yv=ae({},tl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ac=Xe(yv),wv=ae({},Ro,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Va}),Sv=Xe(wv),xv=ae({},_r,{propertyName:0,elapsedTime:0,pseudoElement:0}),_v=Xe(xv),Cv=ae({},tl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ev=Xe(Cv),kv=[9,13,27,32],Ha=It&&"CompositionEvent"in window,Xr=null;It&&"documentMode"in document&&(Xr=document.documentMode);var bv=It&&"TextEvent"in window&&!Xr,Nf=It&&(!Ha||Xr&&8<Xr&&11>=Xr),uc=" ",cc=!1;function Af(e,t){switch(e){case"keyup":return kv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vn=!1;function Pv(e,t){switch(e){case"compositionend":return Tf(t);case"keypress":return t.which!==32?null:(cc=!0,uc);case"textInput":return e=t.data,e===uc&&cc?null:e;default:return null}}function Rv(e,t){if(Vn)return e==="compositionend"||!Ha&&Af(e,t)?(e=Rf(),ai=Ba=Jt=null,Vn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nf&&t.locale!=="ko"?null:t.data;default:return null}}var Nv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function dc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Nv[e.type]:t==="textarea"}function Of(e,t,n,r){uf(r),t=Ai(t,"onChange"),0<t.length&&(n=new $a("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Zr=null,po=null;function Av(e){Vf(e,0)}function nl(e){var t=Kn(e);if(tf(t))return e}function Tv(e,t){if(e==="change")return t}var Lf=!1;if(It){var Ml;if(It){var Fl="oninput"in document;if(!Fl){var fc=document.createElement("div");fc.setAttribute("oninput","return;"),Fl=typeof fc.oninput=="function"}Ml=Fl}else Ml=!1;Lf=Ml&&(!document.documentMode||9<document.documentMode)}function pc(){Zr&&(Zr.detachEvent("onpropertychange",Df),po=Zr=null)}function Df(e){if(e.propertyName==="value"&&nl(po)){var t=[];Of(t,po,e,za(e)),pf(Av,t)}}function Ov(e,t,n){e==="focusin"?(pc(),Zr=t,po=n,Zr.attachEvent("onpropertychange",Df)):e==="focusout"&&pc()}function Lv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return nl(po)}function Dv(e,t){if(e==="click")return nl(t)}function Iv(e,t){if(e==="input"||e==="change")return nl(t)}function zv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ht=typeof Object.is=="function"?Object.is:zv;function ho(e,t){if(ht(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!vs.call(t,o)||!ht(e[o],t[o]))return!1}return!0}function hc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function mc(e,t){var n=hc(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=hc(n)}}function If(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?If(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function zf(){for(var e=window,t=Ci();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Ci(e.document)}return t}function Wa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Mv(e){var t=zf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&If(n.ownerDocument.documentElement,n)){if(r!==null&&Wa(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=mc(n,i);var l=mc(n,r);o&&l&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Fv=It&&"documentMode"in document&&11>=document.documentMode,Hn=null,zs=null,Jr=null,Ms=!1;function gc(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ms||Hn==null||Hn!==Ci(r)||(r=Hn,"selectionStart"in r&&Wa(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Jr&&ho(Jr,r)||(Jr=r,r=Ai(zs,"onSelect"),0<r.length&&(t=new $a("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Hn)))}function Vo(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Wn={animationend:Vo("Animation","AnimationEnd"),animationiteration:Vo("Animation","AnimationIteration"),animationstart:Vo("Animation","AnimationStart"),transitionend:Vo("Transition","TransitionEnd")},jl={},Mf={};It&&(Mf=document.createElement("div").style,"AnimationEvent"in window||(delete Wn.animationend.animation,delete Wn.animationiteration.animation,delete Wn.animationstart.animation),"TransitionEvent"in window||delete Wn.transitionend.transition);function rl(e){if(jl[e])return jl[e];if(!Wn[e])return e;var t=Wn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Mf)return jl[e]=t[n];return e}var Ff=rl("animationend"),jf=rl("animationiteration"),Uf=rl("animationstart"),Bf=rl("transitionend"),$f=new Map,vc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function hn(e,t){$f.set(e,t),Ln(t,[e])}for(var Ul=0;Ul<vc.length;Ul++){var Bl=vc[Ul],jv=Bl.toLowerCase(),Uv=Bl[0].toUpperCase()+Bl.slice(1);hn(jv,"on"+Uv)}hn(Ff,"onAnimationEnd");hn(jf,"onAnimationIteration");hn(Uf,"onAnimationStart");hn("dblclick","onDoubleClick");hn("focusin","onFocus");hn("focusout","onBlur");hn(Bf,"onTransitionEnd");dr("onMouseEnter",["mouseout","mouseover"]);dr("onMouseLeave",["mouseout","mouseover"]);dr("onPointerEnter",["pointerout","pointerover"]);dr("onPointerLeave",["pointerout","pointerover"]);Ln("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ln("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ln("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ln("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ln("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ln("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Kr));function yc(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,jg(r,t,void 0,e),e.currentTarget=null}function Vf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var s=r[l],a=s.instance,u=s.currentTarget;if(s=s.listener,a!==i&&o.isPropagationStopped())break e;yc(o,s,u),i=a}else for(l=0;l<r.length;l++){if(s=r[l],a=s.instance,u=s.currentTarget,s=s.listener,a!==i&&o.isPropagationStopped())break e;yc(o,s,u),i=a}}}if(ki)throw e=Os,ki=!1,Os=null,e}function re(e,t){var n=t[$s];n===void 0&&(n=t[$s]=new Set);var r=e+"__bubble";n.has(r)||(Hf(t,e,2,!1),n.add(r))}function $l(e,t,n){var r=0;t&&(r|=4),Hf(n,e,r,t)}var Ho="_reactListening"+Math.random().toString(36).slice(2);function mo(e){if(!e[Ho]){e[Ho]=!0,Xd.forEach(function(n){n!=="selectionchange"&&(Bv.has(n)||$l(n,!1,e),$l(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ho]||(t[Ho]=!0,$l("selectionchange",!1,t))}}function Hf(e,t,n,r){switch(Pf(t)){case 1:var o=tv;break;case 4:o=nv;break;default:o=Ua}n=o.bind(null,t,n,e),o=void 0,!Ts||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Vl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var s=r.stateNode.containerInfo;if(s===o||s.nodeType===8&&s.parentNode===o)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;l=l.return}for(;s!==null;){if(l=xn(s),l===null)return;if(a=l.tag,a===5||a===6){r=i=l;continue e}s=s.parentNode}}r=r.return}pf(function(){var u=i,d=za(n),m=[];e:{var h=$f.get(e);if(h!==void 0){var y=$a,w=e;switch(e){case"keypress":if(ui(n)===0)break e;case"keydown":case"keyup":y=vv;break;case"focusin":w="focus",y=zl;break;case"focusout":w="blur",y=zl;break;case"beforeblur":case"afterblur":y=zl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=lc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=iv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=Sv;break;case Ff:case jf:case Uf:y=av;break;case Bf:y=_v;break;case"scroll":y=rv;break;case"wheel":y=Ev;break;case"copy":case"cut":case"paste":y=cv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=ac}var p=(t&4)!==0,S=!p&&e==="scroll",f=p?h!==null?h+"Capture":null:h;p=[];for(var c=u,v;c!==null;){v=c;var x=v.stateNode;if(v.tag===5&&x!==null&&(v=x,f!==null&&(x=ao(c,f),x!=null&&p.push(go(c,x,v)))),S)break;c=c.return}0<p.length&&(h=new y(h,w,null,n,d),m.push({event:h,listeners:p}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",h&&n!==Ns&&(w=n.relatedTarget||n.fromElement)&&(xn(w)||w[zt]))break e;if((y||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,y?(w=n.relatedTarget||n.toElement,y=u,w=w?xn(w):null,w!==null&&(S=Dn(w),w!==S||w.tag!==5&&w.tag!==6)&&(w=null)):(y=null,w=u),y!==w)){if(p=lc,x="onMouseLeave",f="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(p=ac,x="onPointerLeave",f="onPointerEnter",c="pointer"),S=y==null?h:Kn(y),v=w==null?h:Kn(w),h=new p(x,c+"leave",y,n,d),h.target=S,h.relatedTarget=v,x=null,xn(d)===u&&(p=new p(f,c+"enter",w,n,d),p.target=v,p.relatedTarget=S,x=p),S=x,y&&w)t:{for(p=y,f=w,c=0,v=p;v;v=In(v))c++;for(v=0,x=f;x;x=In(x))v++;for(;0<c-v;)p=In(p),c--;for(;0<v-c;)f=In(f),v--;for(;c--;){if(p===f||f!==null&&p===f.alternate)break t;p=In(p),f=In(f)}p=null}else p=null;y!==null&&wc(m,h,y,p,!1),w!==null&&S!==null&&wc(m,S,w,p,!0)}}e:{if(h=u?Kn(u):window,y=h.nodeName&&h.nodeName.toLowerCase(),y==="select"||y==="input"&&h.type==="file")var C=Tv;else if(dc(h))if(Lf)C=Iv;else{C=Lv;var A=Ov}else(y=h.nodeName)&&y.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(C=Dv);if(C&&(C=C(e,u))){Of(m,C,n,d);break e}A&&A(e,h,u),e==="focusout"&&(A=h._wrapperState)&&A.controlled&&h.type==="number"&&Es(h,"number",h.value)}switch(A=u?Kn(u):window,e){case"focusin":(dc(A)||A.contentEditable==="true")&&(Hn=A,zs=u,Jr=null);break;case"focusout":Jr=zs=Hn=null;break;case"mousedown":Ms=!0;break;case"contextmenu":case"mouseup":case"dragend":Ms=!1,gc(m,n,d);break;case"selectionchange":if(Fv)break;case"keydown":case"keyup":gc(m,n,d)}var _;if(Ha)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Vn?Af(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Nf&&n.locale!=="ko"&&(Vn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Vn&&(_=Rf()):(Jt=d,Ba="value"in Jt?Jt.value:Jt.textContent,Vn=!0)),A=Ai(u,P),0<A.length&&(P=new sc(P,e,null,n,d),m.push({event:P,listeners:A}),_?P.data=_:(_=Tf(n),_!==null&&(P.data=_)))),(_=bv?Pv(e,n):Rv(e,n))&&(u=Ai(u,"onBeforeInput"),0<u.length&&(d=new sc("onBeforeInput","beforeinput",null,n,d),m.push({event:d,listeners:u}),d.data=_))}Vf(m,t)})}function go(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ai(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=ao(e,n),i!=null&&r.unshift(go(e,i,o)),i=ao(e,t),i!=null&&r.push(go(e,i,o))),e=e.return}return r}function In(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function wc(e,t,n,r,o){for(var i=t._reactName,l=[];n!==null&&n!==r;){var s=n,a=s.alternate,u=s.stateNode;if(a!==null&&a===r)break;s.tag===5&&u!==null&&(s=u,o?(a=ao(n,i),a!=null&&l.unshift(go(n,a,s))):o||(a=ao(n,i),a!=null&&l.push(go(n,a,s)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var $v=/\r\n?/g,Vv=/\u0000|\uFFFD/g;function Sc(e){return(typeof e=="string"?e:""+e).replace($v,`
`).replace(Vv,"")}function Wo(e,t,n){if(t=Sc(t),Sc(e)!==t&&n)throw Error(L(425))}function Ti(){}var Fs=null,js=null;function Us(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Bs=typeof setTimeout=="function"?setTimeout:void 0,Hv=typeof clearTimeout=="function"?clearTimeout:void 0,xc=typeof Promise=="function"?Promise:void 0,Wv=typeof queueMicrotask=="function"?queueMicrotask:typeof xc<"u"?function(e){return xc.resolve(null).then(e).catch(Kv)}:Bs;function Kv(e){setTimeout(function(){throw e})}function Hl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),fo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);fo(t)}function rn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function _c(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Cr=Math.random().toString(36).slice(2),St="__reactFiber$"+Cr,vo="__reactProps$"+Cr,zt="__reactContainer$"+Cr,$s="__reactEvents$"+Cr,Gv="__reactListeners$"+Cr,Qv="__reactHandles$"+Cr;function xn(e){var t=e[St];if(t)return t;for(var n=e.parentNode;n;){if(t=n[zt]||n[St]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=_c(e);e!==null;){if(n=e[St])return n;e=_c(e)}return t}e=n,n=e.parentNode}return null}function No(e){return e=e[St]||e[zt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Kn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function ol(e){return e[vo]||null}var Vs=[],Gn=-1;function mn(e){return{current:e}}function oe(e){0>Gn||(e.current=Vs[Gn],Vs[Gn]=null,Gn--)}function te(e,t){Gn++,Vs[Gn]=e.current,e.current=t}var cn={},Re=mn(cn),Fe=mn(!1),bn=cn;function fr(e,t){var n=e.type.contextTypes;if(!n)return cn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function je(e){return e=e.childContextTypes,e!=null}function Oi(){oe(Fe),oe(Re)}function Cc(e,t,n){if(Re.current!==cn)throw Error(L(168));te(Re,t),te(Fe,n)}function Wf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(L(108,Og(e)||"Unknown",o));return ae({},n,r)}function Li(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cn,bn=Re.current,te(Re,e),te(Fe,Fe.current),!0}function Ec(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=Wf(e,t,bn),r.__reactInternalMemoizedMergedChildContext=e,oe(Fe),oe(Re),te(Re,e)):oe(Fe),te(Fe,n)}var Tt=null,il=!1,Wl=!1;function Kf(e){Tt===null?Tt=[e]:Tt.push(e)}function Yv(e){il=!0,Kf(e)}function gn(){if(!Wl&&Tt!==null){Wl=!0;var e=0,t=Z;try{var n=Tt;for(Z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Tt=null,il=!1}catch(o){throw Tt!==null&&(Tt=Tt.slice(e+1)),vf(Ma,gn),o}finally{Z=t,Wl=!1}}return null}var Qn=[],Yn=0,Di=null,Ii=0,qe=[],et=0,Pn=null,Ot=1,Lt="";function wn(e,t){Qn[Yn++]=Ii,Qn[Yn++]=Di,Di=e,Ii=t}function Gf(e,t,n){qe[et++]=Ot,qe[et++]=Lt,qe[et++]=Pn,Pn=e;var r=Ot;e=Lt;var o=32-ft(r)-1;r&=~(1<<o),n+=1;var i=32-ft(t)+o;if(30<i){var l=o-o%5;i=(r&(1<<l)-1).toString(32),r>>=l,o-=l,Ot=1<<32-ft(t)+o|n<<o|r,Lt=i+e}else Ot=1<<i|n<<o|r,Lt=e}function Ka(e){e.return!==null&&(wn(e,1),Gf(e,1,0))}function Ga(e){for(;e===Di;)Di=Qn[--Yn],Qn[Yn]=null,Ii=Qn[--Yn],Qn[Yn]=null;for(;e===Pn;)Pn=qe[--et],qe[et]=null,Lt=qe[--et],qe[et]=null,Ot=qe[--et],qe[et]=null}var Ke=null,We=null,ie=!1,dt=null;function Qf(e,t){var n=nt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function kc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ke=e,We=rn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ke=e,We=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Pn!==null?{id:Ot,overflow:Lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=nt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ke=e,We=null,!0):!1;default:return!1}}function Hs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ws(e){if(ie){var t=We;if(t){var n=t;if(!kc(e,t)){if(Hs(e))throw Error(L(418));t=rn(n.nextSibling);var r=Ke;t&&kc(e,t)?Qf(r,n):(e.flags=e.flags&-4097|2,ie=!1,Ke=e)}}else{if(Hs(e))throw Error(L(418));e.flags=e.flags&-4097|2,ie=!1,Ke=e}}}function bc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ke=e}function Ko(e){if(e!==Ke)return!1;if(!ie)return bc(e),ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Us(e.type,e.memoizedProps)),t&&(t=We)){if(Hs(e))throw Yf(),Error(L(418));for(;t;)Qf(e,t),t=rn(t.nextSibling)}if(bc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){We=rn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}We=null}}else We=Ke?rn(e.stateNode.nextSibling):null;return!0}function Yf(){for(var e=We;e;)e=rn(e.nextSibling)}function pr(){We=Ke=null,ie=!1}function Qa(e){dt===null?dt=[e]:dt.push(e)}var Xv=$t.ReactCurrentBatchConfig;function Mr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var s=o.refs;l===null?delete s[i]:s[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function Go(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Pc(e){var t=e._init;return t(e._payload)}function Xf(e){function t(f,c){if(e){var v=f.deletions;v===null?(f.deletions=[c],f.flags|=16):v.push(c)}}function n(f,c){if(!e)return null;for(;c!==null;)t(f,c),c=c.sibling;return null}function r(f,c){for(f=new Map;c!==null;)c.key!==null?f.set(c.key,c):f.set(c.index,c),c=c.sibling;return f}function o(f,c){return f=an(f,c),f.index=0,f.sibling=null,f}function i(f,c,v){return f.index=v,e?(v=f.alternate,v!==null?(v=v.index,v<c?(f.flags|=2,c):v):(f.flags|=2,c)):(f.flags|=1048576,c)}function l(f){return e&&f.alternate===null&&(f.flags|=2),f}function s(f,c,v,x){return c===null||c.tag!==6?(c=Jl(v,f.mode,x),c.return=f,c):(c=o(c,v),c.return=f,c)}function a(f,c,v,x){var C=v.type;return C===$n?d(f,c,v.props.children,x,v.key):c!==null&&(c.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Gt&&Pc(C)===c.type)?(x=o(c,v.props),x.ref=Mr(f,c,v),x.return=f,x):(x=gi(v.type,v.key,v.props,null,f.mode,x),x.ref=Mr(f,c,v),x.return=f,x)}function u(f,c,v,x){return c===null||c.tag!==4||c.stateNode.containerInfo!==v.containerInfo||c.stateNode.implementation!==v.implementation?(c=ql(v,f.mode,x),c.return=f,c):(c=o(c,v.children||[]),c.return=f,c)}function d(f,c,v,x,C){return c===null||c.tag!==7?(c=kn(v,f.mode,x,C),c.return=f,c):(c=o(c,v),c.return=f,c)}function m(f,c,v){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Jl(""+c,f.mode,v),c.return=f,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case zo:return v=gi(c.type,c.key,c.props,null,f.mode,v),v.ref=Mr(f,null,c),v.return=f,v;case Bn:return c=ql(c,f.mode,v),c.return=f,c;case Gt:var x=c._init;return m(f,x(c._payload),v)}if(Hr(c)||Or(c))return c=kn(c,f.mode,v,null),c.return=f,c;Go(f,c)}return null}function h(f,c,v,x){var C=c!==null?c.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return C!==null?null:s(f,c,""+v,x);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case zo:return v.key===C?a(f,c,v,x):null;case Bn:return v.key===C?u(f,c,v,x):null;case Gt:return C=v._init,h(f,c,C(v._payload),x)}if(Hr(v)||Or(v))return C!==null?null:d(f,c,v,x,null);Go(f,v)}return null}function y(f,c,v,x,C){if(typeof x=="string"&&x!==""||typeof x=="number")return f=f.get(v)||null,s(c,f,""+x,C);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case zo:return f=f.get(x.key===null?v:x.key)||null,a(c,f,x,C);case Bn:return f=f.get(x.key===null?v:x.key)||null,u(c,f,x,C);case Gt:var A=x._init;return y(f,c,v,A(x._payload),C)}if(Hr(x)||Or(x))return f=f.get(v)||null,d(c,f,x,C,null);Go(c,x)}return null}function w(f,c,v,x){for(var C=null,A=null,_=c,P=c=0,z=null;_!==null&&P<v.length;P++){_.index>P?(z=_,_=null):z=_.sibling;var E=h(f,_,v[P],x);if(E===null){_===null&&(_=z);break}e&&_&&E.alternate===null&&t(f,_),c=i(E,c,P),A===null?C=E:A.sibling=E,A=E,_=z}if(P===v.length)return n(f,_),ie&&wn(f,P),C;if(_===null){for(;P<v.length;P++)_=m(f,v[P],x),_!==null&&(c=i(_,c,P),A===null?C=_:A.sibling=_,A=_);return ie&&wn(f,P),C}for(_=r(f,_);P<v.length;P++)z=y(_,f,P,v[P],x),z!==null&&(e&&z.alternate!==null&&_.delete(z.key===null?P:z.key),c=i(z,c,P),A===null?C=z:A.sibling=z,A=z);return e&&_.forEach(function(O){return t(f,O)}),ie&&wn(f,P),C}function p(f,c,v,x){var C=Or(v);if(typeof C!="function")throw Error(L(150));if(v=C.call(v),v==null)throw Error(L(151));for(var A=C=null,_=c,P=c=0,z=null,E=v.next();_!==null&&!E.done;P++,E=v.next()){_.index>P?(z=_,_=null):z=_.sibling;var O=h(f,_,E.value,x);if(O===null){_===null&&(_=z);break}e&&_&&O.alternate===null&&t(f,_),c=i(O,c,P),A===null?C=O:A.sibling=O,A=O,_=z}if(E.done)return n(f,_),ie&&wn(f,P),C;if(_===null){for(;!E.done;P++,E=v.next())E=m(f,E.value,x),E!==null&&(c=i(E,c,P),A===null?C=E:A.sibling=E,A=E);return ie&&wn(f,P),C}for(_=r(f,_);!E.done;P++,E=v.next())E=y(_,f,P,E.value,x),E!==null&&(e&&E.alternate!==null&&_.delete(E.key===null?P:E.key),c=i(E,c,P),A===null?C=E:A.sibling=E,A=E);return e&&_.forEach(function(R){return t(f,R)}),ie&&wn(f,P),C}function S(f,c,v,x){if(typeof v=="object"&&v!==null&&v.type===$n&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case zo:e:{for(var C=v.key,A=c;A!==null;){if(A.key===C){if(C=v.type,C===$n){if(A.tag===7){n(f,A.sibling),c=o(A,v.props.children),c.return=f,f=c;break e}}else if(A.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Gt&&Pc(C)===A.type){n(f,A.sibling),c=o(A,v.props),c.ref=Mr(f,A,v),c.return=f,f=c;break e}n(f,A);break}else t(f,A);A=A.sibling}v.type===$n?(c=kn(v.props.children,f.mode,x,v.key),c.return=f,f=c):(x=gi(v.type,v.key,v.props,null,f.mode,x),x.ref=Mr(f,c,v),x.return=f,f=x)}return l(f);case Bn:e:{for(A=v.key;c!==null;){if(c.key===A)if(c.tag===4&&c.stateNode.containerInfo===v.containerInfo&&c.stateNode.implementation===v.implementation){n(f,c.sibling),c=o(c,v.children||[]),c.return=f,f=c;break e}else{n(f,c);break}else t(f,c);c=c.sibling}c=ql(v,f.mode,x),c.return=f,f=c}return l(f);case Gt:return A=v._init,S(f,c,A(v._payload),x)}if(Hr(v))return w(f,c,v,x);if(Or(v))return p(f,c,v,x);Go(f,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,c!==null&&c.tag===6?(n(f,c.sibling),c=o(c,v),c.return=f,f=c):(n(f,c),c=Jl(v,f.mode,x),c.return=f,f=c),l(f)):n(f,c)}return S}var hr=Xf(!0),Zf=Xf(!1),zi=mn(null),Mi=null,Xn=null,Ya=null;function Xa(){Ya=Xn=Mi=null}function Za(e){var t=zi.current;oe(zi),e._currentValue=t}function Ks(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ir(e,t){Mi=e,Ya=Xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Me=!0),e.firstContext=null)}function ot(e){var t=e._currentValue;if(Ya!==e)if(e={context:e,memoizedValue:t,next:null},Xn===null){if(Mi===null)throw Error(L(308));Xn=e,Mi.dependencies={lanes:0,firstContext:e}}else Xn=Xn.next=e;return t}var _n=null;function Ja(e){_n===null?_n=[e]:_n.push(e)}function Jf(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Ja(t)):(n.next=o.next,o.next=n),t.interleaved=n,Mt(e,r)}function Mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Qt=!1;function qa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function qf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function on(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,Y&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Mt(e,n)}return o=r.interleaved,o===null?(t.next=t,Ja(r)):(t.next=o.next,o.next=t),r.interleaved=t,Mt(e,n)}function ci(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fa(e,n)}}function Rc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Fi(e,t,n,r){var o=e.updateQueue;Qt=!1;var i=o.firstBaseUpdate,l=o.lastBaseUpdate,s=o.shared.pending;if(s!==null){o.shared.pending=null;var a=s,u=a.next;a.next=null,l===null?i=u:l.next=u,l=a;var d=e.alternate;d!==null&&(d=d.updateQueue,s=d.lastBaseUpdate,s!==l&&(s===null?d.firstBaseUpdate=u:s.next=u,d.lastBaseUpdate=a))}if(i!==null){var m=o.baseState;l=0,d=u=a=null,s=i;do{var h=s.lane,y=s.eventTime;if((r&h)===h){d!==null&&(d=d.next={eventTime:y,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var w=e,p=s;switch(h=t,y=n,p.tag){case 1:if(w=p.payload,typeof w=="function"){m=w.call(y,m,h);break e}m=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=p.payload,h=typeof w=="function"?w.call(y,m,h):w,h==null)break e;m=ae({},m,h);break e;case 2:Qt=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,h=o.effects,h===null?o.effects=[s]:h.push(s))}else y={eventTime:y,lane:h,tag:s.tag,payload:s.payload,callback:s.callback,next:null},d===null?(u=d=y,a=m):d=d.next=y,l|=h;if(s=s.next,s===null){if(s=o.shared.pending,s===null)break;h=s,s=h.next,h.next=null,o.lastBaseUpdate=h,o.shared.pending=null}}while(!0);if(d===null&&(a=m),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do l|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);Nn|=l,e.lanes=l,e.memoizedState=m}}function Nc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(L(191,o));o.call(r)}}}var Ao={},Ct=mn(Ao),yo=mn(Ao),wo=mn(Ao);function Cn(e){if(e===Ao)throw Error(L(174));return e}function eu(e,t){switch(te(wo,t),te(yo,e),te(Ct,Ao),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:bs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=bs(t,e)}oe(Ct),te(Ct,t)}function mr(){oe(Ct),oe(yo),oe(wo)}function ep(e){Cn(wo.current);var t=Cn(Ct.current),n=bs(t,e.type);t!==n&&(te(yo,e),te(Ct,n))}function tu(e){yo.current===e&&(oe(Ct),oe(yo))}var le=mn(0);function ji(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Kl=[];function nu(){for(var e=0;e<Kl.length;e++)Kl[e]._workInProgressVersionPrimary=null;Kl.length=0}var di=$t.ReactCurrentDispatcher,Gl=$t.ReactCurrentBatchConfig,Rn=0,se=null,me=null,ye=null,Ui=!1,qr=!1,So=0,Zv=0;function ke(){throw Error(L(321))}function ru(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ht(e[n],t[n]))return!1;return!0}function ou(e,t,n,r,o,i){if(Rn=i,se=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,di.current=e===null||e.memoizedState===null?ty:ny,e=n(r,o),qr){i=0;do{if(qr=!1,So=0,25<=i)throw Error(L(301));i+=1,ye=me=null,t.updateQueue=null,di.current=ry,e=n(r,o)}while(qr)}if(di.current=Bi,t=me!==null&&me.next!==null,Rn=0,ye=me=se=null,Ui=!1,t)throw Error(L(300));return e}function iu(){var e=So!==0;return So=0,e}function wt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ye===null?se.memoizedState=ye=e:ye=ye.next=e,ye}function it(){if(me===null){var e=se.alternate;e=e!==null?e.memoizedState:null}else e=me.next;var t=ye===null?se.memoizedState:ye.next;if(t!==null)ye=t,me=e;else{if(e===null)throw Error(L(310));me=e,e={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},ye===null?se.memoizedState=ye=e:ye=ye.next=e}return ye}function xo(e,t){return typeof t=="function"?t(e):t}function Ql(e){var t=it(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=me,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var l=o.next;o.next=i.next,i.next=l}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var s=l=null,a=null,u=i;do{var d=u.lane;if((Rn&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var m={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(s=a=m,l=r):a=a.next=m,se.lanes|=d,Nn|=d}u=u.next}while(u!==null&&u!==i);a===null?l=r:a.next=s,ht(r,t.memoizedState)||(Me=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,se.lanes|=i,Nn|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Yl(e){var t=it(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var l=o=o.next;do i=e(i,l.action),l=l.next;while(l!==o);ht(i,t.memoizedState)||(Me=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function tp(){}function np(e,t){var n=se,r=it(),o=t(),i=!ht(r.memoizedState,o);if(i&&(r.memoizedState=o,Me=!0),r=r.queue,lu(ip.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ye!==null&&ye.memoizedState.tag&1){if(n.flags|=2048,_o(9,op.bind(null,n,r,o,t),void 0,null),we===null)throw Error(L(349));Rn&30||rp(n,t,o)}return o}function rp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=se.updateQueue,t===null?(t={lastEffect:null,stores:null},se.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function op(e,t,n,r){t.value=n,t.getSnapshot=r,lp(t)&&sp(e)}function ip(e,t,n){return n(function(){lp(t)&&sp(e)})}function lp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ht(e,n)}catch{return!0}}function sp(e){var t=Mt(e,1);t!==null&&pt(t,e,1,-1)}function Ac(e){var t=wt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=ey.bind(null,se,e),[t.memoizedState,e]}function _o(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=se.updateQueue,t===null?(t={lastEffect:null,stores:null},se.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ap(){return it().memoizedState}function fi(e,t,n,r){var o=wt();se.flags|=e,o.memoizedState=_o(1|t,n,void 0,r===void 0?null:r)}function ll(e,t,n,r){var o=it();r=r===void 0?null:r;var i=void 0;if(me!==null){var l=me.memoizedState;if(i=l.destroy,r!==null&&ru(r,l.deps)){o.memoizedState=_o(t,n,i,r);return}}se.flags|=e,o.memoizedState=_o(1|t,n,i,r)}function Tc(e,t){return fi(8390656,8,e,t)}function lu(e,t){return ll(2048,8,e,t)}function up(e,t){return ll(4,2,e,t)}function cp(e,t){return ll(4,4,e,t)}function dp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function fp(e,t,n){return n=n!=null?n.concat([e]):null,ll(4,4,dp.bind(null,t,e),n)}function su(){}function pp(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ru(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function hp(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ru(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function mp(e,t,n){return Rn&21?(ht(n,t)||(n=Sf(),se.lanes|=n,Nn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Me=!0),e.memoizedState=n)}function Jv(e,t){var n=Z;Z=n!==0&&4>n?n:4,e(!0);var r=Gl.transition;Gl.transition={};try{e(!1),t()}finally{Z=n,Gl.transition=r}}function gp(){return it().memoizedState}function qv(e,t,n){var r=sn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},vp(e))yp(t,n);else if(n=Jf(e,t,n,r),n!==null){var o=Ae();pt(n,e,r,o),wp(n,t,r)}}function ey(e,t,n){var r=sn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(vp(e))yp(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,s=i(l,n);if(o.hasEagerState=!0,o.eagerState=s,ht(s,l)){var a=t.interleaved;a===null?(o.next=o,Ja(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Jf(e,t,o,r),n!==null&&(o=Ae(),pt(n,e,r,o),wp(n,t,r))}}function vp(e){var t=e.alternate;return e===se||t!==null&&t===se}function yp(e,t){qr=Ui=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function wp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fa(e,n)}}var Bi={readContext:ot,useCallback:ke,useContext:ke,useEffect:ke,useImperativeHandle:ke,useInsertionEffect:ke,useLayoutEffect:ke,useMemo:ke,useReducer:ke,useRef:ke,useState:ke,useDebugValue:ke,useDeferredValue:ke,useTransition:ke,useMutableSource:ke,useSyncExternalStore:ke,useId:ke,unstable_isNewReconciler:!1},ty={readContext:ot,useCallback:function(e,t){return wt().memoizedState=[e,t===void 0?null:t],e},useContext:ot,useEffect:Tc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,fi(4194308,4,dp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return fi(4194308,4,e,t)},useInsertionEffect:function(e,t){return fi(4,2,e,t)},useMemo:function(e,t){var n=wt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=wt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qv.bind(null,se,e),[r.memoizedState,e]},useRef:function(e){var t=wt();return e={current:e},t.memoizedState=e},useState:Ac,useDebugValue:su,useDeferredValue:function(e){return wt().memoizedState=e},useTransition:function(){var e=Ac(!1),t=e[0];return e=Jv.bind(null,e[1]),wt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=se,o=wt();if(ie){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),we===null)throw Error(L(349));Rn&30||rp(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Tc(ip.bind(null,r,i,e),[e]),r.flags|=2048,_o(9,op.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=wt(),t=we.identifierPrefix;if(ie){var n=Lt,r=Ot;n=(r&~(1<<32-ft(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=So++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Zv++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ny={readContext:ot,useCallback:pp,useContext:ot,useEffect:lu,useImperativeHandle:fp,useInsertionEffect:up,useLayoutEffect:cp,useMemo:hp,useReducer:Ql,useRef:ap,useState:function(){return Ql(xo)},useDebugValue:su,useDeferredValue:function(e){var t=it();return mp(t,me.memoizedState,e)},useTransition:function(){var e=Ql(xo)[0],t=it().memoizedState;return[e,t]},useMutableSource:tp,useSyncExternalStore:np,useId:gp,unstable_isNewReconciler:!1},ry={readContext:ot,useCallback:pp,useContext:ot,useEffect:lu,useImperativeHandle:fp,useInsertionEffect:up,useLayoutEffect:cp,useMemo:hp,useReducer:Yl,useRef:ap,useState:function(){return Yl(xo)},useDebugValue:su,useDeferredValue:function(e){var t=it();return me===null?t.memoizedState=e:mp(t,me.memoizedState,e)},useTransition:function(){var e=Yl(xo)[0],t=it().memoizedState;return[e,t]},useMutableSource:tp,useSyncExternalStore:np,useId:gp,unstable_isNewReconciler:!1};function ut(e,t){if(e&&e.defaultProps){t=ae({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Gs(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ae({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var sl={isMounted:function(e){return(e=e._reactInternals)?Dn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ae(),o=sn(e),i=Dt(r,o);i.payload=t,n!=null&&(i.callback=n),t=on(e,i,o),t!==null&&(pt(t,e,o,r),ci(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ae(),o=sn(e),i=Dt(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=on(e,i,o),t!==null&&(pt(t,e,o,r),ci(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ae(),r=sn(e),o=Dt(n,r);o.tag=2,t!=null&&(o.callback=t),t=on(e,o,r),t!==null&&(pt(t,e,r,n),ci(t,e,r))}};function Oc(e,t,n,r,o,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!ho(n,r)||!ho(o,i):!0}function Sp(e,t,n){var r=!1,o=cn,i=t.contextType;return typeof i=="object"&&i!==null?i=ot(i):(o=je(t)?bn:Re.current,r=t.contextTypes,i=(r=r!=null)?fr(e,o):cn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=sl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Lc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&sl.enqueueReplaceState(t,t.state,null)}function Qs(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},qa(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=ot(i):(i=je(t)?bn:Re.current,o.context=fr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Gs(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&sl.enqueueReplaceState(o,o.state,null),Fi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function gr(e,t){try{var n="",r=t;do n+=Tg(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Xl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ys(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var oy=typeof WeakMap=="function"?WeakMap:Map;function xp(e,t,n){n=Dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Vi||(Vi=!0,ia=r),Ys(e,t)},n}function _p(e,t,n){n=Dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ys(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ys(e,t),typeof r!="function"&&(ln===null?ln=new Set([this]):ln.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function Dc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new oy;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=yy.bind(null,e,t,n),t.then(e,e))}function Ic(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function zc(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Dt(-1,1),t.tag=2,on(n,t,1))),n.lanes|=1),e)}var iy=$t.ReactCurrentOwner,Me=!1;function Ne(e,t,n,r){t.child=e===null?Zf(t,null,n,r):hr(t,e.child,n,r)}function Mc(e,t,n,r,o){n=n.render;var i=t.ref;return ir(t,o),r=ou(e,t,n,r,i,o),n=iu(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ft(e,t,o)):(ie&&n&&Ka(t),t.flags|=1,Ne(e,t,r,o),t.child)}function Fc(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!mu(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Cp(e,t,i,r,o)):(e=gi(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:ho,n(l,r)&&e.ref===t.ref)return Ft(e,t,o)}return t.flags|=1,e=an(i,r),e.ref=t.ref,e.return=t,t.child=e}function Cp(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(ho(i,r)&&e.ref===t.ref)if(Me=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Me=!0);else return t.lanes=e.lanes,Ft(e,t,o)}return Xs(e,t,n,r,o)}function Ep(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},te(Jn,Ve),Ve|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,te(Jn,Ve),Ve|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,te(Jn,Ve),Ve|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,te(Jn,Ve),Ve|=r;return Ne(e,t,o,n),t.child}function kp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Xs(e,t,n,r,o){var i=je(n)?bn:Re.current;return i=fr(t,i),ir(t,o),n=ou(e,t,n,r,i,o),r=iu(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ft(e,t,o)):(ie&&r&&Ka(t),t.flags|=1,Ne(e,t,n,o),t.child)}function jc(e,t,n,r,o){if(je(n)){var i=!0;Li(t)}else i=!1;if(ir(t,o),t.stateNode===null)pi(e,t),Sp(t,n,r),Qs(t,n,r,o),r=!0;else if(e===null){var l=t.stateNode,s=t.memoizedProps;l.props=s;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=je(n)?bn:Re.current,u=fr(t,u));var d=n.getDerivedStateFromProps,m=typeof d=="function"||typeof l.getSnapshotBeforeUpdate=="function";m||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==r||a!==u)&&Lc(t,l,r,u),Qt=!1;var h=t.memoizedState;l.state=h,Fi(t,r,l,o),a=t.memoizedState,s!==r||h!==a||Fe.current||Qt?(typeof d=="function"&&(Gs(t,n,d,r),a=t.memoizedState),(s=Qt||Oc(t,n,s,r,h,a,u))?(m||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=s):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,qf(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ut(t.type,s),l.props=u,m=t.pendingProps,h=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=ot(a):(a=je(n)?bn:Re.current,a=fr(t,a));var y=n.getDerivedStateFromProps;(d=typeof y=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(s!==m||h!==a)&&Lc(t,l,r,a),Qt=!1,h=t.memoizedState,l.state=h,Fi(t,r,l,o);var w=t.memoizedState;s!==m||h!==w||Fe.current||Qt?(typeof y=="function"&&(Gs(t,n,y,r),w=t.memoizedState),(u=Qt||Oc(t,n,u,r,h,w,a)||!1)?(d||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,w,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,w,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),l.props=r,l.state=w,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Zs(e,t,n,r,i,o)}function Zs(e,t,n,r,o,i){kp(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return o&&Ec(t,n,!1),Ft(e,t,i);r=t.stateNode,iy.current=t;var s=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=hr(t,e.child,null,i),t.child=hr(t,null,s,i)):Ne(e,t,s,i),t.memoizedState=r.state,o&&Ec(t,n,!0),t.child}function bp(e){var t=e.stateNode;t.pendingContext?Cc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Cc(e,t.context,!1),eu(e,t.containerInfo)}function Uc(e,t,n,r,o){return pr(),Qa(o),t.flags|=256,Ne(e,t,n,r),t.child}var Js={dehydrated:null,treeContext:null,retryLane:0};function qs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Pp(e,t,n){var r=t.pendingProps,o=le.current,i=!1,l=(t.flags&128)!==0,s;if((s=l)||(s=e!==null&&e.memoizedState===null?!1:(o&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),te(le,o&1),e===null)return Ws(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=cl(l,r,0,null),e=kn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=qs(n),t.memoizedState=Js,e):au(t,l));if(o=e.memoizedState,o!==null&&(s=o.dehydrated,s!==null))return ly(e,t,l,r,s,o,n);if(i){i=r.fallback,l=t.mode,o=e.child,s=o.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=an(o,a),r.subtreeFlags=o.subtreeFlags&14680064),s!==null?i=an(s,i):(i=kn(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?qs(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=Js,r}return i=e.child,e=i.sibling,r=an(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function au(e,t){return t=cl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Qo(e,t,n,r){return r!==null&&Qa(r),hr(t,e.child,null,n),e=au(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ly(e,t,n,r,o,i,l){if(n)return t.flags&256?(t.flags&=-257,r=Xl(Error(L(422))),Qo(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=cl({mode:"visible",children:r.children},o,0,null),i=kn(i,o,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&hr(t,e.child,null,l),t.child.memoizedState=qs(l),t.memoizedState=Js,i);if(!(t.mode&1))return Qo(e,t,l,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(L(419)),r=Xl(i,r,void 0),Qo(e,t,l,r)}if(s=(l&e.childLanes)!==0,Me||s){if(r=we,r!==null){switch(l&-l){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|l)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Mt(e,o),pt(r,e,o,-1))}return hu(),r=Xl(Error(L(421))),Qo(e,t,l,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=wy.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,We=rn(o.nextSibling),Ke=t,ie=!0,dt=null,e!==null&&(qe[et++]=Ot,qe[et++]=Lt,qe[et++]=Pn,Ot=e.id,Lt=e.overflow,Pn=t),t=au(t,r.children),t.flags|=4096,t)}function Bc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ks(e.return,t,n)}function Zl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Rp(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(Ne(e,t,r.children,n),r=le.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Bc(e,n,t);else if(e.tag===19)Bc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(le,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&ji(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Zl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&ji(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Zl(t,!0,n,null,i);break;case"together":Zl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function pi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ft(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Nn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=an(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=an(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function sy(e,t,n){switch(t.tag){case 3:bp(t),pr();break;case 5:ep(t);break;case 1:je(t.type)&&Li(t);break;case 4:eu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;te(zi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(te(le,le.current&1),t.flags|=128,null):n&t.child.childLanes?Pp(e,t,n):(te(le,le.current&1),e=Ft(e,t,n),e!==null?e.sibling:null);te(le,le.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Rp(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),te(le,le.current),r)break;return null;case 22:case 23:return t.lanes=0,Ep(e,t,n)}return Ft(e,t,n)}var Np,ea,Ap,Tp;Np=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ea=function(){};Ap=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Cn(Ct.current);var i=null;switch(n){case"input":o=_s(e,o),r=_s(e,r),i=[];break;case"select":o=ae({},o,{value:void 0}),r=ae({},r,{value:void 0}),i=[];break;case"textarea":o=ks(e,o),r=ks(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ti)}Ps(n,r);var l;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var s=o[u];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(lo.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(s=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==s&&(a!=null||s!=null))if(u==="style")if(s){for(l in s)!s.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&s[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,s=s?s.__html:void 0,a!=null&&s!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(lo.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&re("scroll",e),i||s===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Tp=function(e,t,n,r){n!==r&&(t.flags|=4)};function Fr(e,t){if(!ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function be(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ay(e,t,n){var r=t.pendingProps;switch(Ga(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return be(t),null;case 1:return je(t.type)&&Oi(),be(t),null;case 3:return r=t.stateNode,mr(),oe(Fe),oe(Re),nu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ko(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,dt!==null&&(aa(dt),dt=null))),ea(e,t),be(t),null;case 5:tu(t);var o=Cn(wo.current);if(n=t.type,e!==null&&t.stateNode!=null)Ap(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return be(t),null}if(e=Cn(Ct.current),Ko(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[St]=t,r[vo]=i,e=(t.mode&1)!==0,n){case"dialog":re("cancel",r),re("close",r);break;case"iframe":case"object":case"embed":re("load",r);break;case"video":case"audio":for(o=0;o<Kr.length;o++)re(Kr[o],r);break;case"source":re("error",r);break;case"img":case"image":case"link":re("error",r),re("load",r);break;case"details":re("toggle",r);break;case"input":Xu(r,i),re("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},re("invalid",r);break;case"textarea":Ju(r,i),re("invalid",r)}Ps(n,i),o=null;for(var l in i)if(i.hasOwnProperty(l)){var s=i[l];l==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&Wo(r.textContent,s,e),o=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&Wo(r.textContent,s,e),o=["children",""+s]):lo.hasOwnProperty(l)&&s!=null&&l==="onScroll"&&re("scroll",r)}switch(n){case"input":Mo(r),Zu(r,i,!0);break;case"textarea":Mo(r),qu(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Ti)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=of(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[St]=t,e[vo]=r,Np(e,t,!1,!1),t.stateNode=e;e:{switch(l=Rs(n,r),n){case"dialog":re("cancel",e),re("close",e),o=r;break;case"iframe":case"object":case"embed":re("load",e),o=r;break;case"video":case"audio":for(o=0;o<Kr.length;o++)re(Kr[o],e);o=r;break;case"source":re("error",e),o=r;break;case"img":case"image":case"link":re("error",e),re("load",e),o=r;break;case"details":re("toggle",e),o=r;break;case"input":Xu(e,r),o=_s(e,r),re("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ae({},r,{value:void 0}),re("invalid",e);break;case"textarea":Ju(e,r),o=ks(e,r),re("invalid",e);break;default:o=r}Ps(n,o),s=o;for(i in s)if(s.hasOwnProperty(i)){var a=s[i];i==="style"?af(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&lf(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&so(e,a):typeof a=="number"&&so(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(lo.hasOwnProperty(i)?a!=null&&i==="onScroll"&&re("scroll",e):a!=null&&Oa(e,i,a,l))}switch(n){case"input":Mo(e),Zu(e,r,!1);break;case"textarea":Mo(e),qu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+un(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?tr(e,!!r.multiple,i,!1):r.defaultValue!=null&&tr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Ti)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return be(t),null;case 6:if(e&&t.stateNode!=null)Tp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=Cn(wo.current),Cn(Ct.current),Ko(t)){if(r=t.stateNode,n=t.memoizedProps,r[St]=t,(i=r.nodeValue!==n)&&(e=Ke,e!==null))switch(e.tag){case 3:Wo(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Wo(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[St]=t,t.stateNode=r}return be(t),null;case 13:if(oe(le),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ie&&We!==null&&t.mode&1&&!(t.flags&128))Yf(),pr(),t.flags|=98560,i=!1;else if(i=Ko(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(L(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(L(317));i[St]=t}else pr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;be(t),i=!1}else dt!==null&&(aa(dt),dt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||le.current&1?ge===0&&(ge=3):hu())),t.updateQueue!==null&&(t.flags|=4),be(t),null);case 4:return mr(),ea(e,t),e===null&&mo(t.stateNode.containerInfo),be(t),null;case 10:return Za(t.type._context),be(t),null;case 17:return je(t.type)&&Oi(),be(t),null;case 19:if(oe(le),i=t.memoizedState,i===null)return be(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Fr(i,!1);else{if(ge!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=ji(e),l!==null){for(t.flags|=128,Fr(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return te(le,le.current&1|2),t.child}e=e.sibling}i.tail!==null&&ce()>vr&&(t.flags|=128,r=!0,Fr(i,!1),t.lanes=4194304)}else{if(!r)if(e=ji(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Fr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!ie)return be(t),null}else 2*ce()-i.renderingStartTime>vr&&n!==1073741824&&(t.flags|=128,r=!0,Fr(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ce(),t.sibling=null,n=le.current,te(le,r?n&1|2:n&1),t):(be(t),null);case 22:case 23:return pu(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ve&1073741824&&(be(t),t.subtreeFlags&6&&(t.flags|=8192)):be(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function uy(e,t){switch(Ga(t),t.tag){case 1:return je(t.type)&&Oi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mr(),oe(Fe),oe(Re),nu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return tu(t),null;case 13:if(oe(le),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));pr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(le),null;case 4:return mr(),null;case 10:return Za(t.type._context),null;case 22:case 23:return pu(),null;case 24:return null;default:return null}}var Yo=!1,Pe=!1,cy=typeof WeakSet=="function"?WeakSet:Set,M=null;function Zn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ue(e,t,r)}else n.current=null}function ta(e,t,n){try{n()}catch(r){ue(e,t,r)}}var $c=!1;function dy(e,t){if(Fs=Ri,e=zf(),Wa(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,s=-1,a=-1,u=0,d=0,m=e,h=null;t:for(;;){for(var y;m!==n||o!==0&&m.nodeType!==3||(s=l+o),m!==i||r!==0&&m.nodeType!==3||(a=l+r),m.nodeType===3&&(l+=m.nodeValue.length),(y=m.firstChild)!==null;)h=m,m=y;for(;;){if(m===e)break t;if(h===n&&++u===o&&(s=l),h===i&&++d===r&&(a=l),(y=m.nextSibling)!==null)break;m=h,h=m.parentNode}m=y}n=s===-1||a===-1?null:{start:s,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(js={focusedElem:e,selectionRange:n},Ri=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var p=w.memoizedProps,S=w.memoizedState,f=t.stateNode,c=f.getSnapshotBeforeUpdate(t.elementType===t.type?p:ut(t.type,p),S);f.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(x){ue(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return w=$c,$c=!1,w}function eo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&ta(t,n,i)}o=o.next}while(o!==r)}}function al(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function na(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Op(e){var t=e.alternate;t!==null&&(e.alternate=null,Op(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[St],delete t[vo],delete t[$s],delete t[Gv],delete t[Qv])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Lp(e){return e.tag===5||e.tag===3||e.tag===4}function Vc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Lp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ra(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ti));else if(r!==4&&(e=e.child,e!==null))for(ra(e,t,n),e=e.sibling;e!==null;)ra(e,t,n),e=e.sibling}function oa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(oa(e,t,n),e=e.sibling;e!==null;)oa(e,t,n),e=e.sibling}var Se=null,ct=!1;function Vt(e,t,n){for(n=n.child;n!==null;)Dp(e,t,n),n=n.sibling}function Dp(e,t,n){if(_t&&typeof _t.onCommitFiberUnmount=="function")try{_t.onCommitFiberUnmount(el,n)}catch{}switch(n.tag){case 5:Pe||Zn(n,t);case 6:var r=Se,o=ct;Se=null,Vt(e,t,n),Se=r,ct=o,Se!==null&&(ct?(e=Se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Se.removeChild(n.stateNode));break;case 18:Se!==null&&(ct?(e=Se,n=n.stateNode,e.nodeType===8?Hl(e.parentNode,n):e.nodeType===1&&Hl(e,n),fo(e)):Hl(Se,n.stateNode));break;case 4:r=Se,o=ct,Se=n.stateNode.containerInfo,ct=!0,Vt(e,t,n),Se=r,ct=o;break;case 0:case 11:case 14:case 15:if(!Pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&ta(n,t,l),o=o.next}while(o!==r)}Vt(e,t,n);break;case 1:if(!Pe&&(Zn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){ue(n,t,s)}Vt(e,t,n);break;case 21:Vt(e,t,n);break;case 22:n.mode&1?(Pe=(r=Pe)||n.memoizedState!==null,Vt(e,t,n),Pe=r):Vt(e,t,n);break;default:Vt(e,t,n)}}function Hc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new cy),t.forEach(function(r){var o=Sy.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function lt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,l=t,s=l;e:for(;s!==null;){switch(s.tag){case 5:Se=s.stateNode,ct=!1;break e;case 3:Se=s.stateNode.containerInfo,ct=!0;break e;case 4:Se=s.stateNode.containerInfo,ct=!0;break e}s=s.return}if(Se===null)throw Error(L(160));Dp(i,l,o),Se=null,ct=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){ue(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Ip(t,e),t=t.sibling}function Ip(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(lt(t,e),vt(e),r&4){try{eo(3,e,e.return),al(3,e)}catch(p){ue(e,e.return,p)}try{eo(5,e,e.return)}catch(p){ue(e,e.return,p)}}break;case 1:lt(t,e),vt(e),r&512&&n!==null&&Zn(n,n.return);break;case 5:if(lt(t,e),vt(e),r&512&&n!==null&&Zn(n,n.return),e.flags&32){var o=e.stateNode;try{so(o,"")}catch(p){ue(e,e.return,p)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,s=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&nf(o,i),Rs(s,l);var u=Rs(s,i);for(l=0;l<a.length;l+=2){var d=a[l],m=a[l+1];d==="style"?af(o,m):d==="dangerouslySetInnerHTML"?lf(o,m):d==="children"?so(o,m):Oa(o,d,m,u)}switch(s){case"input":Cs(o,i);break;case"textarea":rf(o,i);break;case"select":var h=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?tr(o,!!i.multiple,y,!1):h!==!!i.multiple&&(i.defaultValue!=null?tr(o,!!i.multiple,i.defaultValue,!0):tr(o,!!i.multiple,i.multiple?[]:"",!1))}o[vo]=i}catch(p){ue(e,e.return,p)}}break;case 6:if(lt(t,e),vt(e),r&4){if(e.stateNode===null)throw Error(L(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(p){ue(e,e.return,p)}}break;case 3:if(lt(t,e),vt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{fo(t.containerInfo)}catch(p){ue(e,e.return,p)}break;case 4:lt(t,e),vt(e);break;case 13:lt(t,e),vt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(du=ce())),r&4&&Hc(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(Pe=(u=Pe)||d,lt(t,e),Pe=u):lt(t,e),vt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(M=e,d=e.child;d!==null;){for(m=M=d;M!==null;){switch(h=M,y=h.child,h.tag){case 0:case 11:case 14:case 15:eo(4,h,h.return);break;case 1:Zn(h,h.return);var w=h.stateNode;if(typeof w.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(p){ue(r,n,p)}}break;case 5:Zn(h,h.return);break;case 22:if(h.memoizedState!==null){Kc(m);continue}}y!==null?(y.return=h,M=y):Kc(m)}d=d.sibling}e:for(d=null,m=e;;){if(m.tag===5){if(d===null){d=m;try{o=m.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=m.stateNode,a=m.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,s.style.display=sf("display",l))}catch(p){ue(e,e.return,p)}}}else if(m.tag===6){if(d===null)try{m.stateNode.nodeValue=u?"":m.memoizedProps}catch(p){ue(e,e.return,p)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;d===m&&(d=null),m=m.return}d===m&&(d=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:lt(t,e),vt(e),r&4&&Hc(e);break;case 21:break;default:lt(t,e),vt(e)}}function vt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Lp(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(so(o,""),r.flags&=-33);var i=Vc(e);oa(e,i,o);break;case 3:case 4:var l=r.stateNode.containerInfo,s=Vc(e);ra(e,s,l);break;default:throw Error(L(161))}}catch(a){ue(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function fy(e,t,n){M=e,zp(e)}function zp(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var o=M,i=o.child;if(o.tag===22&&r){var l=o.memoizedState!==null||Yo;if(!l){var s=o.alternate,a=s!==null&&s.memoizedState!==null||Pe;s=Yo;var u=Pe;if(Yo=l,(Pe=a)&&!u)for(M=o;M!==null;)l=M,a=l.child,l.tag===22&&l.memoizedState!==null?Gc(o):a!==null?(a.return=l,M=a):Gc(o);for(;i!==null;)M=i,zp(i),i=i.sibling;M=o,Yo=s,Pe=u}Wc(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,M=i):Wc(e)}}function Wc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Pe||al(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Pe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ut(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Nc(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Nc(t,l,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var m=d.dehydrated;m!==null&&fo(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}Pe||t.flags&512&&na(t)}catch(h){ue(t,t.return,h)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Kc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Gc(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{al(4,t)}catch(a){ue(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){ue(t,o,a)}}var i=t.return;try{na(t)}catch(a){ue(t,i,a)}break;case 5:var l=t.return;try{na(t)}catch(a){ue(t,l,a)}}}catch(a){ue(t,t.return,a)}if(t===e){M=null;break}var s=t.sibling;if(s!==null){s.return=t.return,M=s;break}M=t.return}}var py=Math.ceil,$i=$t.ReactCurrentDispatcher,uu=$t.ReactCurrentOwner,rt=$t.ReactCurrentBatchConfig,Y=0,we=null,fe=null,_e=0,Ve=0,Jn=mn(0),ge=0,Co=null,Nn=0,ul=0,cu=0,to=null,ze=null,du=0,vr=1/0,At=null,Vi=!1,ia=null,ln=null,Xo=!1,qt=null,Hi=0,no=0,la=null,hi=-1,mi=0;function Ae(){return Y&6?ce():hi!==-1?hi:hi=ce()}function sn(e){return e.mode&1?Y&2&&_e!==0?_e&-_e:Xv.transition!==null?(mi===0&&(mi=Sf()),mi):(e=Z,e!==0||(e=window.event,e=e===void 0?16:Pf(e.type)),e):1}function pt(e,t,n,r){if(50<no)throw no=0,la=null,Error(L(185));Po(e,n,r),(!(Y&2)||e!==we)&&(e===we&&(!(Y&2)&&(ul|=n),ge===4&&Zt(e,_e)),Ue(e,r),n===1&&Y===0&&!(t.mode&1)&&(vr=ce()+500,il&&gn()))}function Ue(e,t){var n=e.callbackNode;Xg(e,t);var r=Pi(e,e===we?_e:0);if(r===0)n!==null&&nc(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&nc(n),t===1)e.tag===0?Yv(Qc.bind(null,e)):Kf(Qc.bind(null,e)),Wv(function(){!(Y&6)&&gn()}),n=null;else{switch(xf(r)){case 1:n=Ma;break;case 4:n=yf;break;case 16:n=bi;break;case 536870912:n=wf;break;default:n=bi}n=Hp(n,Mp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Mp(e,t){if(hi=-1,mi=0,Y&6)throw Error(L(327));var n=e.callbackNode;if(lr()&&e.callbackNode!==n)return null;var r=Pi(e,e===we?_e:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Wi(e,r);else{t=r;var o=Y;Y|=2;var i=jp();(we!==e||_e!==t)&&(At=null,vr=ce()+500,En(e,t));do try{gy();break}catch(s){Fp(e,s)}while(!0);Xa(),$i.current=i,Y=o,fe!==null?t=0:(we=null,_e=0,t=ge)}if(t!==0){if(t===2&&(o=Ls(e),o!==0&&(r=o,t=sa(e,o))),t===1)throw n=Co,En(e,0),Zt(e,r),Ue(e,ce()),n;if(t===6)Zt(e,r);else{if(o=e.current.alternate,!(r&30)&&!hy(o)&&(t=Wi(e,r),t===2&&(i=Ls(e),i!==0&&(r=i,t=sa(e,i))),t===1))throw n=Co,En(e,0),Zt(e,r),Ue(e,ce()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:Sn(e,ze,At);break;case 3:if(Zt(e,r),(r&130023424)===r&&(t=du+500-ce(),10<t)){if(Pi(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ae(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Bs(Sn.bind(null,e,ze,At),t);break}Sn(e,ze,At);break;case 4:if(Zt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var l=31-ft(r);i=1<<l,l=t[l],l>o&&(o=l),r&=~i}if(r=o,r=ce()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*py(r/1960))-r,10<r){e.timeoutHandle=Bs(Sn.bind(null,e,ze,At),r);break}Sn(e,ze,At);break;case 5:Sn(e,ze,At);break;default:throw Error(L(329))}}}return Ue(e,ce()),e.callbackNode===n?Mp.bind(null,e):null}function sa(e,t){var n=to;return e.current.memoizedState.isDehydrated&&(En(e,t).flags|=256),e=Wi(e,t),e!==2&&(t=ze,ze=n,t!==null&&aa(t)),e}function aa(e){ze===null?ze=e:ze.push.apply(ze,e)}function hy(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!ht(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Zt(e,t){for(t&=~cu,t&=~ul,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ft(t),r=1<<n;e[n]=-1,t&=~r}}function Qc(e){if(Y&6)throw Error(L(327));lr();var t=Pi(e,0);if(!(t&1))return Ue(e,ce()),null;var n=Wi(e,t);if(e.tag!==0&&n===2){var r=Ls(e);r!==0&&(t=r,n=sa(e,r))}if(n===1)throw n=Co,En(e,0),Zt(e,t),Ue(e,ce()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sn(e,ze,At),Ue(e,ce()),null}function fu(e,t){var n=Y;Y|=1;try{return e(t)}finally{Y=n,Y===0&&(vr=ce()+500,il&&gn())}}function An(e){qt!==null&&qt.tag===0&&!(Y&6)&&lr();var t=Y;Y|=1;var n=rt.transition,r=Z;try{if(rt.transition=null,Z=1,e)return e()}finally{Z=r,rt.transition=n,Y=t,!(Y&6)&&gn()}}function pu(){Ve=Jn.current,oe(Jn)}function En(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Hv(n)),fe!==null)for(n=fe.return;n!==null;){var r=n;switch(Ga(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Oi();break;case 3:mr(),oe(Fe),oe(Re),nu();break;case 5:tu(r);break;case 4:mr();break;case 13:oe(le);break;case 19:oe(le);break;case 10:Za(r.type._context);break;case 22:case 23:pu()}n=n.return}if(we=e,fe=e=an(e.current,null),_e=Ve=t,ge=0,Co=null,cu=ul=Nn=0,ze=to=null,_n!==null){for(t=0;t<_n.length;t++)if(n=_n[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=o,r.next=l}n.pending=r}_n=null}return e}function Fp(e,t){do{var n=fe;try{if(Xa(),di.current=Bi,Ui){for(var r=se.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ui=!1}if(Rn=0,ye=me=se=null,qr=!1,So=0,uu.current=null,n===null||n.return===null){ge=1,Co=t,fe=null;break}e:{var i=e,l=n.return,s=n,a=t;if(t=_e,s.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=s,m=d.tag;if(!(d.mode&1)&&(m===0||m===11||m===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=Ic(l);if(y!==null){y.flags&=-257,zc(y,l,s,i,t),y.mode&1&&Dc(i,u,t),t=y,a=u;var w=t.updateQueue;if(w===null){var p=new Set;p.add(a),t.updateQueue=p}else w.add(a);break e}else{if(!(t&1)){Dc(i,u,t),hu();break e}a=Error(L(426))}}else if(ie&&s.mode&1){var S=Ic(l);if(S!==null){!(S.flags&65536)&&(S.flags|=256),zc(S,l,s,i,t),Qa(gr(a,s));break e}}i=a=gr(a,s),ge!==4&&(ge=2),to===null?to=[i]:to.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var f=xp(i,a,t);Rc(i,f);break e;case 1:s=a;var c=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(ln===null||!ln.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=_p(i,s,t);Rc(i,x);break e}}i=i.return}while(i!==null)}Bp(n)}catch(C){t=C,fe===n&&n!==null&&(fe=n=n.return);continue}break}while(!0)}function jp(){var e=$i.current;return $i.current=Bi,e===null?Bi:e}function hu(){(ge===0||ge===3||ge===2)&&(ge=4),we===null||!(Nn&268435455)&&!(ul&268435455)||Zt(we,_e)}function Wi(e,t){var n=Y;Y|=2;var r=jp();(we!==e||_e!==t)&&(At=null,En(e,t));do try{my();break}catch(o){Fp(e,o)}while(!0);if(Xa(),Y=n,$i.current=r,fe!==null)throw Error(L(261));return we=null,_e=0,ge}function my(){for(;fe!==null;)Up(fe)}function gy(){for(;fe!==null&&!Bg();)Up(fe)}function Up(e){var t=Vp(e.alternate,e,Ve);e.memoizedProps=e.pendingProps,t===null?Bp(e):fe=t,uu.current=null}function Bp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=uy(n,t),n!==null){n.flags&=32767,fe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ge=6,fe=null;return}}else if(n=ay(n,t,Ve),n!==null){fe=n;return}if(t=t.sibling,t!==null){fe=t;return}fe=t=e}while(t!==null);ge===0&&(ge=5)}function Sn(e,t,n){var r=Z,o=rt.transition;try{rt.transition=null,Z=1,vy(e,t,n,r)}finally{rt.transition=o,Z=r}return null}function vy(e,t,n,r){do lr();while(qt!==null);if(Y&6)throw Error(L(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Zg(e,i),e===we&&(fe=we=null,_e=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Xo||(Xo=!0,Hp(bi,function(){return lr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=rt.transition,rt.transition=null;var l=Z;Z=1;var s=Y;Y|=4,uu.current=null,dy(e,n),Ip(n,e),Mv(js),Ri=!!Fs,js=Fs=null,e.current=n,fy(n),$g(),Y=s,Z=l,rt.transition=i}else e.current=n;if(Xo&&(Xo=!1,qt=e,Hi=o),i=e.pendingLanes,i===0&&(ln=null),Wg(n.stateNode),Ue(e,ce()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Vi)throw Vi=!1,e=ia,ia=null,e;return Hi&1&&e.tag!==0&&lr(),i=e.pendingLanes,i&1?e===la?no++:(no=0,la=e):no=0,gn(),null}function lr(){if(qt!==null){var e=xf(Hi),t=rt.transition,n=Z;try{if(rt.transition=null,Z=16>e?16:e,qt===null)var r=!1;else{if(e=qt,qt=null,Hi=0,Y&6)throw Error(L(331));var o=Y;for(Y|=4,M=e.current;M!==null;){var i=M,l=i.child;if(M.flags&16){var s=i.deletions;if(s!==null){for(var a=0;a<s.length;a++){var u=s[a];for(M=u;M!==null;){var d=M;switch(d.tag){case 0:case 11:case 15:eo(8,d,i)}var m=d.child;if(m!==null)m.return=d,M=m;else for(;M!==null;){d=M;var h=d.sibling,y=d.return;if(Op(d),d===u){M=null;break}if(h!==null){h.return=y,M=h;break}M=y}}}var w=i.alternate;if(w!==null){var p=w.child;if(p!==null){w.child=null;do{var S=p.sibling;p.sibling=null,p=S}while(p!==null)}}M=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,M=l;else e:for(;M!==null;){if(i=M,i.flags&2048)switch(i.tag){case 0:case 11:case 15:eo(9,i,i.return)}var f=i.sibling;if(f!==null){f.return=i.return,M=f;break e}M=i.return}}var c=e.current;for(M=c;M!==null;){l=M;var v=l.child;if(l.subtreeFlags&2064&&v!==null)v.return=l,M=v;else e:for(l=c;M!==null;){if(s=M,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:al(9,s)}}catch(C){ue(s,s.return,C)}if(s===l){M=null;break e}var x=s.sibling;if(x!==null){x.return=s.return,M=x;break e}M=s.return}}if(Y=o,gn(),_t&&typeof _t.onPostCommitFiberRoot=="function")try{_t.onPostCommitFiberRoot(el,e)}catch{}r=!0}return r}finally{Z=n,rt.transition=t}}return!1}function Yc(e,t,n){t=gr(n,t),t=xp(e,t,1),e=on(e,t,1),t=Ae(),e!==null&&(Po(e,1,t),Ue(e,t))}function ue(e,t,n){if(e.tag===3)Yc(e,e,n);else for(;t!==null;){if(t.tag===3){Yc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ln===null||!ln.has(r))){e=gr(n,e),e=_p(t,e,1),t=on(t,e,1),e=Ae(),t!==null&&(Po(t,1,e),Ue(t,e));break}}t=t.return}}function yy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ae(),e.pingedLanes|=e.suspendedLanes&n,we===e&&(_e&n)===n&&(ge===4||ge===3&&(_e&130023424)===_e&&500>ce()-du?En(e,0):cu|=n),Ue(e,t)}function $p(e,t){t===0&&(e.mode&1?(t=Uo,Uo<<=1,!(Uo&130023424)&&(Uo=4194304)):t=1);var n=Ae();e=Mt(e,t),e!==null&&(Po(e,t,n),Ue(e,n))}function wy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),$p(e,n)}function Sy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),$p(e,n)}var Vp;Vp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Fe.current)Me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Me=!1,sy(e,t,n);Me=!!(e.flags&131072)}else Me=!1,ie&&t.flags&1048576&&Gf(t,Ii,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;pi(e,t),e=t.pendingProps;var o=fr(t,Re.current);ir(t,n),o=ou(null,t,r,e,o,n);var i=iu();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,je(r)?(i=!0,Li(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,qa(t),o.updater=sl,t.stateNode=o,o._reactInternals=t,Qs(t,r,e,n),t=Zs(null,t,r,!0,i,n)):(t.tag=0,ie&&i&&Ka(t),Ne(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(pi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=_y(r),e=ut(r,e),o){case 0:t=Xs(null,t,r,e,n);break e;case 1:t=jc(null,t,r,e,n);break e;case 11:t=Mc(null,t,r,e,n);break e;case 14:t=Fc(null,t,r,ut(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),Xs(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),jc(e,t,r,o,n);case 3:e:{if(bp(t),e===null)throw Error(L(387));r=t.pendingProps,i=t.memoizedState,o=i.element,qf(e,t),Fi(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=gr(Error(L(423)),t),t=Uc(e,t,r,n,o);break e}else if(r!==o){o=gr(Error(L(424)),t),t=Uc(e,t,r,n,o);break e}else for(We=rn(t.stateNode.containerInfo.firstChild),Ke=t,ie=!0,dt=null,n=Zf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(pr(),r===o){t=Ft(e,t,n);break e}Ne(e,t,r,n)}t=t.child}return t;case 5:return ep(t),e===null&&Ws(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,l=o.children,Us(r,o)?l=null:i!==null&&Us(r,i)&&(t.flags|=32),kp(e,t),Ne(e,t,l,n),t.child;case 6:return e===null&&Ws(t),null;case 13:return Pp(e,t,n);case 4:return eu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hr(t,null,r,n):Ne(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),Mc(e,t,r,o,n);case 7:return Ne(e,t,t.pendingProps,n),t.child;case 8:return Ne(e,t,t.pendingProps.children,n),t.child;case 12:return Ne(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,l=o.value,te(zi,r._currentValue),r._currentValue=l,i!==null)if(ht(i.value,l)){if(i.children===o.children&&!Fe.current){t=Ft(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){l=i.child;for(var a=s.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Dt(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ks(i.return,n,t),s.lanes|=n;break}a=a.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(L(341));l.lanes|=n,s=l.alternate,s!==null&&(s.lanes|=n),Ks(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}Ne(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,ir(t,n),o=ot(o),r=r(o),t.flags|=1,Ne(e,t,r,n),t.child;case 14:return r=t.type,o=ut(r,t.pendingProps),o=ut(r.type,o),Fc(e,t,r,o,n);case 15:return Cp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ut(r,o),pi(e,t),t.tag=1,je(r)?(e=!0,Li(t)):e=!1,ir(t,n),Sp(t,r,o),Qs(t,r,o,n),Zs(null,t,r,!0,e,n);case 19:return Rp(e,t,n);case 22:return Ep(e,t,n)}throw Error(L(156,t.tag))};function Hp(e,t){return vf(e,t)}function xy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(e,t,n,r){return new xy(e,t,n,r)}function mu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function _y(e){if(typeof e=="function")return mu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Da)return 11;if(e===Ia)return 14}return 2}function an(e,t){var n=e.alternate;return n===null?(n=nt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function gi(e,t,n,r,o,i){var l=2;if(r=e,typeof e=="function")mu(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case $n:return kn(n.children,o,i,t);case La:l=8,o|=8;break;case ys:return e=nt(12,n,t,o|2),e.elementType=ys,e.lanes=i,e;case ws:return e=nt(13,n,t,o),e.elementType=ws,e.lanes=i,e;case Ss:return e=nt(19,n,t,o),e.elementType=Ss,e.lanes=i,e;case qd:return cl(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Zd:l=10;break e;case Jd:l=9;break e;case Da:l=11;break e;case Ia:l=14;break e;case Gt:l=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=nt(l,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function kn(e,t,n,r){return e=nt(7,e,r,t),e.lanes=n,e}function cl(e,t,n,r){return e=nt(22,e,r,t),e.elementType=qd,e.lanes=n,e.stateNode={isHidden:!1},e}function Jl(e,t,n){return e=nt(6,e,null,t),e.lanes=n,e}function ql(e,t,n){return t=nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Cy(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ll(0),this.expirationTimes=Ll(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ll(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function gu(e,t,n,r,o,i,l,s,a){return e=new Cy(e,t,n,s,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=nt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},qa(i),e}function Ey(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Bn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Wp(e){if(!e)return cn;e=e._reactInternals;e:{if(Dn(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(je(n))return Wf(e,n,t)}return t}function Kp(e,t,n,r,o,i,l,s,a){return e=gu(n,r,!0,e,o,i,l,s,a),e.context=Wp(null),n=e.current,r=Ae(),o=sn(n),i=Dt(r,o),i.callback=t??null,on(n,i,o),e.current.lanes=o,Po(e,o,r),Ue(e,r),e}function dl(e,t,n,r){var o=t.current,i=Ae(),l=sn(o);return n=Wp(n),t.context===null?t.context=n:t.pendingContext=n,t=Dt(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=on(o,t,l),e!==null&&(pt(e,o,l,i),ci(e,o,l)),l}function Ki(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Xc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function vu(e,t){Xc(e,t),(e=e.alternate)&&Xc(e,t)}function ky(){return null}var Gp=typeof reportError=="function"?reportError:function(e){console.error(e)};function yu(e){this._internalRoot=e}fl.prototype.render=yu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));dl(e,t,null,null)};fl.prototype.unmount=yu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;An(function(){dl(null,e,null,null)}),t[zt]=null}};function fl(e){this._internalRoot=e}fl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ef();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Xt.length&&t!==0&&t<Xt[n].priority;n++);Xt.splice(n,0,e),n===0&&bf(e)}};function wu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function pl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Zc(){}function by(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Ki(l);i.call(u)}}var l=Kp(t,r,e,0,null,!1,!1,"",Zc);return e._reactRootContainer=l,e[zt]=l.current,mo(e.nodeType===8?e.parentNode:e),An(),l}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var s=r;r=function(){var u=Ki(a);s.call(u)}}var a=gu(e,0,!1,null,null,!1,!1,"",Zc);return e._reactRootContainer=a,e[zt]=a.current,mo(e.nodeType===8?e.parentNode:e),An(function(){dl(t,a,n,r)}),a}function hl(e,t,n,r,o){var i=n._reactRootContainer;if(i){var l=i;if(typeof o=="function"){var s=o;o=function(){var a=Ki(l);s.call(a)}}dl(t,l,e,o)}else l=by(n,t,e,o,r);return Ki(l)}_f=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Wr(t.pendingLanes);n!==0&&(Fa(t,n|1),Ue(t,ce()),!(Y&6)&&(vr=ce()+500,gn()))}break;case 13:An(function(){var r=Mt(e,1);if(r!==null){var o=Ae();pt(r,e,1,o)}}),vu(e,1)}};ja=function(e){if(e.tag===13){var t=Mt(e,134217728);if(t!==null){var n=Ae();pt(t,e,134217728,n)}vu(e,134217728)}};Cf=function(e){if(e.tag===13){var t=sn(e),n=Mt(e,t);if(n!==null){var r=Ae();pt(n,e,t,r)}vu(e,t)}};Ef=function(){return Z};kf=function(e,t){var n=Z;try{return Z=e,t()}finally{Z=n}};As=function(e,t,n){switch(t){case"input":if(Cs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=ol(r);if(!o)throw Error(L(90));tf(r),Cs(r,o)}}}break;case"textarea":rf(e,n);break;case"select":t=n.value,t!=null&&tr(e,!!n.multiple,t,!1)}};df=fu;ff=An;var Py={usingClientEntryPoint:!1,Events:[No,Kn,ol,uf,cf,fu]},jr={findFiberByHostInstance:xn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ry={bundleType:jr.bundleType,version:jr.version,rendererPackageName:jr.rendererPackageName,rendererConfig:jr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:$t.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=mf(e),e===null?null:e.stateNode},findFiberByHostInstance:jr.findFiberByHostInstance||ky,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Zo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Zo.isDisabled&&Zo.supportsFiber)try{el=Zo.inject(Ry),_t=Zo}catch{}}Ye.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Py;Ye.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!wu(t))throw Error(L(200));return Ey(e,t,null,n)};Ye.createRoot=function(e,t){if(!wu(e))throw Error(L(299));var n=!1,r="",o=Gp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=gu(e,1,!1,null,null,n,!1,r,o),e[zt]=t.current,mo(e.nodeType===8?e.parentNode:e),new yu(t)};Ye.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=mf(t),e=e===null?null:e.stateNode,e};Ye.flushSync=function(e){return An(e)};Ye.hydrate=function(e,t,n){if(!pl(t))throw Error(L(200));return hl(null,e,t,!0,n)};Ye.hydrateRoot=function(e,t,n){if(!wu(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",l=Gp;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=Kp(t,null,e,1,n??null,o,!1,i,l),e[zt]=t.current,mo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new fl(t)};Ye.render=function(e,t,n){if(!pl(t))throw Error(L(200));return hl(null,e,t,!1,n)};Ye.unmountComponentAtNode=function(e){if(!pl(e))throw Error(L(40));return e._reactRootContainer?(An(function(){hl(null,null,e,!1,function(){e._reactRootContainer=null,e[zt]=null})}),!0):!1};Ye.unstable_batchedUpdates=fu;Ye.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!pl(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return hl(e,t,n,!1,r)};Ye.version="18.3.1-next-f1338f8080-20240426";function Qp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Qp)}catch(e){console.error(e)}}Qp(),Gd.exports=Ye;var Er=Gd.exports;const Ny=Id(Er);var Jc=Er;Ku.createRoot=Jc.createRoot,Ku.hydrateRoot=Jc.hydrateRoot;function Ie(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)}function vi(e,t,n,r,o){if(typeof t=="function"?e!==t||!o:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return t.set(e,n),n}var zn,yt,Mn,yi;const Be="__TAURI_TO_IPC_KEY__";function Yp(e,t=!1){return window.__TAURI_INTERNALS__.transformCallback(e,t)}class bx{constructor(){this.__TAURI_CHANNEL_MARKER__=!0,zn.set(this,()=>{}),yt.set(this,0),Mn.set(this,[]),this.id=Yp(({message:t,id:n})=>{if(n==Ie(this,yt,"f"))for(Ie(this,zn,"f").call(this,t),vi(this,yt,Ie(this,yt,"f")+1);Ie(this,yt,"f")in Ie(this,Mn,"f");){const r=Ie(this,Mn,"f")[Ie(this,yt,"f")];Ie(this,zn,"f").call(this,r),delete Ie(this,Mn,"f")[Ie(this,yt,"f")],vi(this,yt,Ie(this,yt,"f")+1)}else Ie(this,Mn,"f")[n]=t})}set onmessage(t){vi(this,zn,t)}get onmessage(){return Ie(this,zn,"f")}[(zn=new WeakMap,yt=new WeakMap,Mn=new WeakMap,Be)](){return`__CHANNEL__:${this.id}`}toJSON(){return this[Be]()}}async function T(e,t={},n){return window.__TAURI_INTERNALS__.invoke(e,t,n)}class Xp{get rid(){return Ie(this,yi,"f")}constructor(t){yi.set(this,void 0),vi(this,yi,t)}async close(){return T("plugin:resources|close",{rid:this.rid})}}yi=new WeakMap;var ro=(e=>(e[e.Byte=0]="Byte",e[e.Short=1]="Short",e[e.Int=2]="Int",e[e.Float=3]="Float",e[e.Buffer=4]="Buffer",e[e.SignedShort=5]="SignedShort",e[e.SignedInt=6]="SignedInt",e))(ro||{});function wi(e,t){return new Promise((n,r)=>{const i=["read_memory_byte","read_memory_short","read_memory_int","read_memory_float","read_memory_buffer","read_memory_signed_short","read_memory_signed_int"][t];T(i,{address:e}).then(l=>{n(l)}).catch(l=>{console.error("Error reading memory",l),r(l)})})}function Ay(e,t){return new Promise((n,r)=>{T("read_memory_buffer",{address:e,size:t}).then(o=>{n(o)}).catch(o=>{console.error("Error reading memory buffer",o),r(o)})})}function Px(e,t,n){return new Promise((r,o)=>{const l=["write_memory_byte","write_memory_short","write_memory_int","write_memory_float","write_memory_buffer","write_memory_signed_short","write_memory_signed_int"][n],s={address:e};n===4?s.buffer=t:s.newValue=t,T(l,s).then(()=>{r()}).catch(a=>{console.error("Error writing memory",a),o(a)})})}function Rx(e,t){return new Promise((n,r)=>{T("set_memory_protection",{address:e,size:t}).then(()=>{n()}).catch(o=>{console.error("Error setting memory protection",o),r(o)})})}const $e={Dead:1,NearDeath:2,Sleep:4,Poison:8,Sadness:16,Fury:32,Confusion:64,Silence:128,Haste:256,Slow:512,Stop:1024,Frog:2048,Small:4096,SlowNumb:8192,Petrify:16384,Regen:32768,Barrier:65536,MBarrier:131072,Reflect:262144,Dual:524288,Shield:1048576,DeathSentence:2097152,Berserk:8388608,Peerless:16777216,Paralyze:33554432,Darkness:67108864,DualDrain:134217728,DeathForce:268435456,Resist:536870912,LuckyGirl:1073741824,Imprisoned:2147483648},Nx=$e.Sadness|$e.Haste|$e.Regen|$e.Barrier|$e.MBarrier|$e.Reflect|$e.Shield|$e.Berserk|$e.Peerless|$e.Resist|$e.LuckyGirl;var Ty=(e=>(e.General="general",e.Field="field",e.World="world",e.Battle="battle",e.Party="party",e.Chocobos="chocobos",e.Variables="variables",e))(Ty||{}),st=(e=>(e[e.None=0]="None",e[e.Field=1]="Field",e[e.Battle=2]="Battle",e[e.World=3]="World",e[e.Menu=5]="Menu",e[e.Highway=6]="Highway",e[e.Chocobo=7]="Chocobo",e[e.SnowBoard=8]="SnowBoard",e[e.Condor=9]="Condor",e[e.Submarine=10]="Submarine",e[e.Jet=11]="Jet",e[e.ChangeDisc=12]="ChangeDisc",e[e.Snowboard2=14]="Snowboard2",e[e.Quit=19]="Quit",e[e.Start=20]="Start",e[e.BattleSwirl=23]="BattleSwirl",e[e.Ending=25]="Ending",e[e.GameOver=26]="GameOver",e[e.Intro=27]="Intro",e[e.Credits=28]="Credits",e))(st||{}),oo=(e=>(e[e.Off=0]="Off",e[e.Normal=1]="Normal",e[e.Max=2]="Max",e))(oo||{}),Oy=(e=>(e[e.Cloud=0]="Cloud",e[e.Tifa=1]="Tifa",e[e.Cid=2]="Cid",e[e.Highwind=3]="Highwind",e[e.WildChocobo=4]="WildChocobo",e[e.TinyBronco=5]="TinyBronco",e[e.Buggy=6]="Buggy",e[e.JunonCanon=7]="JunonCanon",e[e.CargoShip=8]="CargoShip",e[e.HighwindPropellers=9]="HighwindPropellers",e[e.DiamondWeapon=10]="DiamondWeapon",e[e.UltimateWeapon=11]="UltimateWeapon",e[e.FortCondor=12]="FortCondor",e[e.Submarine=13]="Submarine",e[e.GoldSaucer=14]="GoldSaucer",e[e.RocketTownRocket=15]="RocketTownRocket",e[e.RocketTownPad=16]="RocketTownPad",e[e.SunkenGelnika=17]="SunkenGelnika",e[e.UnderwaterReactor=18]="UnderwaterReactor",e[e.Chocobo=19]="Chocobo",e[e.MidgarCanon=20]="MidgarCanon",e[e.Unknown1=21]="Unknown1",e[e.Unknown2=22]="Unknown2",e[e.Unknown3=23]="Unknown3",e[e.NorthCraterBarrier=24]="NorthCraterBarrier",e[e.AncientForest=25]="AncientForest",e[e.KeyOfTheAncients=26]="KeyOfTheAncients",e[e.Unknown4=27]="Unknown4",e[e.RedSubmarine=28]="RedSubmarine",e[e.RubyWeapon=29]="RubyWeapon",e[e.EmeraldWeapon=30]="EmeraldWeapon",e))(Oy||{}),Ly=(e=>(e[e.Grass=0]="Grass",e[e.Forest=1]="Forest",e[e.Mountain=2]="Mountain",e[e.Sea=3]="Sea",e[e.RiverCrossing=4]="RiverCrossing",e[e.River=5]="River",e[e.Water=6]="Water",e[e.Swamp=7]="Swamp",e[e.Desert=8]="Desert",e[e.Wasteland=9]="Wasteland",e[e.Snow=10]="Snow",e[e.Riverside=11]="Riverside",e[e.Cliff=12]="Cliff",e[e.CorelBridge=13]="CorelBridge",e[e.WutaiBridge=14]="WutaiBridge",e[e.Unused1=15]="Unused1",e[e.Hillside=16]="Hillside",e[e.Beach=17]="Beach",e[e.SubPen=18]="SubPen",e[e.Canyon=19]="Canyon",e[e.MountainPass=20]="MountainPass",e[e.UnknownCliff=21]="UnknownCliff",e[e.Waterfall=22]="Waterfall",e[e.Unused2=23]="Unused2",e[e.SaucerDesert=24]="SaucerDesert",e[e.Jungle=25]="Jungle",e[e.Sea2=26]="Sea2",e[e.NorthernCave=27]="NorthernCave",e[e.DesertBorder=28]="DesertBorder",e[e.Bridgehead=29]="Bridgehead",e[e.BackEntrance=30]="BackEntrance",e[e.Unused3=31]="Unused3",e))(Ly||{}),Zp=(e=>(e[e.Fire=0]="Fire",e[e.Ice=1]="Ice",e[e.Lightning=2]="Lightning",e[e.Earth=3]="Earth",e[e.Poison=4]="Poison",e[e.Gravity=5]="Gravity",e[e.Water=6]="Water",e[e.Wind=7]="Wind",e[e.Holy=8]="Holy",e[e.Restorative=9]="Restorative",e[e.Cut=10]="Cut",e[e.Hit=11]="Hit",e[e.Punch=12]="Punch",e[e.Shoot=13]="Shoot",e[e.Scream=14]="Scream",e[e.Hidden=15]="Hidden",e[e.Nothing=255]="Nothing",e))(Zp||{}),Dy=(e=>(e[e.Death=0]="Death",e[e.AutoHit=1]="AutoHit",e[e.DoubleDamage=2]="DoubleDamage",e[e.HalfDamage=4]="HalfDamage",e[e.Nullify=5]="Nullify",e[e.Absorb=6]="Absorb",e[e.FullCure=7]="FullCure",e[e.Nothing=255]="Nothing",e))(Dy||{}),Iy=(e=>(e[e["Midgar Area"]=0]="Midgar Area",e[e["Grasslands Area"]=1]="Grasslands Area",e[e["Junon Area"]=2]="Junon Area",e[e["Corel Area"]=3]="Corel Area",e[e["Gold Saucer Area"]=4]="Gold Saucer Area",e[e["Gongaga Area"]=5]="Gongaga Area",e[e["Cosmo Area"]=6]="Cosmo Area",e[e["Nibel Area"]=7]="Nibel Area",e[e["Rocket Launch Pad Area"]=8]="Rocket Launch Pad Area",e[e["Wutai Area"]=9]="Wutai Area",e[e["Woodlands Area"]=10]="Woodlands Area",e[e["Icicle Area"]=11]="Icicle Area",e[e["Mideel Area"]=12]="Mideel Area",e[e["North Corel Area"]=13]="North Corel Area",e[e["Cactus Island"]=14]="Cactus Island",e[e["Goblin Island"]=15]="Goblin Island",e[e["Round Island"]=16]="Round Island",e[e.Sea=17]="Sea",e[e["Bottom of the Sea"]=18]="Bottom of the Sea",e[e.Glacier=19]="Glacier",e))(Iy||{}),zy=(e=>(e[e.wonderful=1]="wonderful",e[e.great=2]="great",e[e.good=3]="good",e[e["so-so"]=4]="so-so",e[e.average=5]="average",e[e["not bad"]=6]="not bad",e[e.bad=7]="bad",e[e.terrible=8]="terrible",e))(zy||{});function My(){const[e,t]=g.useState(null),[n,r]=g.useState(!0),[o,i]=g.useState(null);return g.useEffect(()=>{async function l(){try{const s=await T("get_ff7_addresses");t(s),r(!1)}catch(s){i(s instanceof Error?s.message:"An unknown error occurred"),r(!1)}}l()},[]),{addresses:e,isLoading:n,error:o}}var qn={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const qc=(e,t)=>e.unstable_is?e.unstable_is(t):t===e,ed=e=>"init"in e,es=e=>!!e.write,td=e=>"v"in e||"e"in e,Jo=e=>{if("e"in e)throw e.e;if((qn?"production":void 0)!=="production"&&!("v"in e))throw new Error("[Bug] atom state is not initialized");return e.v},Jp=Symbol(),Su=e=>e[Jp],ua=e=>{var t;return xu(e)&&!((t=Su(e))!=null&&t[1])},Fy=(e,t)=>{const n=Su(e);if(n)n[1]=!0,n[0].forEach(r=>r(t));else if((qn?"production":void 0)!=="production")throw new Error("[Bug] cancelable promise not found")},jy=e=>{if(Su(e))return;const t=[new Set,!1];e[Jp]=t;const n=()=>{t[1]=!0};e.then(n,n),e.onCancel=r=>{t[0].add(r)}},xu=e=>typeof(e==null?void 0:e.then)=="function",qp=(e,t,n)=>{n.p.has(e)||(n.p.add(e),t.then(()=>{n.p.delete(e)},()=>{n.p.delete(e)}))},ts=(e,t,n)=>{const r=n(e),o="v"in r,i=r.v,l=ua(r.v)?r.v:null;if(xu(t)){jy(t);for(const s of r.d.keys())qp(e,t,n(s))}r.v=t,delete r.e,(!o||!Object.is(i,r.v))&&(++r.n,l&&Fy(l,t))},nd=(e,t,n)=>{var r;const o=new Set;for(const i of((r=n.get(e))==null?void 0:r.t)||[])n.has(i)&&o.add(i);for(const i of t.p)o.add(i);return o},Uy=()=>{const e=new Set,t=()=>{e.forEach(n=>n())};return t.add=n=>(e.add(n),()=>{e.delete(n)}),t},ns=()=>{const e={},t=new WeakMap,n=r=>{var o,i;(o=t.get(e))==null||o.forEach(l=>l(r)),(i=t.get(r))==null||i.forEach(l=>l())};return n.add=(r,o)=>{const i=r||e,l=(t.has(i)?t:t.set(i,new Set)).get(i);return l.add(o),()=>{l==null||l.delete(o),l.size||t.delete(i)}},n},By=e=>(e.c||(e.c=ns()),e.m||(e.m=ns()),e.u||(e.u=ns()),e.f||(e.f=Uy()),e),$y=Symbol(),Vy=(e=new WeakMap,t=new WeakMap,n=new WeakMap,r=new Set,o=new Set,i=new Set,l={},s=(h,...y)=>h.read(...y),a=(h,...y)=>h.write(...y),u=(h,y)=>{var w;return(w=h.unstable_onInit)==null?void 0:w.call(h,y)},d=(h,y)=>{var w;return(w=h.onMount)==null?void 0:w.call(h,y)},...m)=>{const h=m[0]||(_=>{if((qn?"production":void 0)!=="production"&&!_)throw new Error("Atom is undefined or null");let P=e.get(_);return P||(P={d:new Map,p:new Set,n:0},e.set(_,P),u==null||u(_,A)),P}),y=m[1]||(()=>{let _,P;const z=E=>{try{E()}catch(O){_||(_=!0,P=O)}};do{l.f&&z(l.f);const E=new Set,O=E.add.bind(E);r.forEach(R=>{var D;return(D=t.get(R))==null?void 0:D.l.forEach(O)}),r.clear(),i.forEach(O),i.clear(),o.forEach(O),o.clear(),E.forEach(z),r.size&&w()}while(r.size||i.size||o.size);if(_)throw P}),w=m[2]||(()=>{var _;const P=[],z=new WeakSet,E=new WeakSet,O=Array.from(r);for(;O.length;){const R=O[O.length-1],D=h(R);if(E.has(R)){O.pop();continue}if(z.has(R)){if(n.get(R)===D.n)P.push([R,D,D.n]);else if((qn?"production":void 0)!=="production"&&n.has(R))throw new Error("[Bug] invalidated atom exists");E.add(R),O.pop();continue}z.add(R);for(const k of nd(R,D,t))z.has(k)||O.push(k)}for(let R=P.length-1;R>=0;--R){const[D,k,U]=P[R];let j=!1;for(const $ of k.d.keys())if($!==D&&r.has($)){j=!0;break}j&&(p(D),c(D),U!==k.n&&(r.add(D),(_=l.c)==null||_.call(l,D))),n.delete(D)}}),p=m[3]||(_=>{var P,z;const E=h(_);if(td(E)&&(t.has(_)&&n.get(_)!==E.n||Array.from(E.d).every(([b,I])=>p(b).n===I)))return E;E.d.clear();let O=!0;const R=()=>{t.has(_)&&(c(_),w(),y())},D=b=>{var I;if(qc(_,b)){const B=h(b);if(!td(B))if(ed(b))ts(b,b.init,h);else throw new Error("no atom init");return Jo(B)}const F=p(b);try{return Jo(F)}finally{E.d.set(b,F.n),ua(E.v)&&qp(_,E.v,F),(I=t.get(b))==null||I.t.add(_),O||R()}};let k,U;const j={get signal(){return k||(k=new AbortController),k.signal},get setSelf(){return(qn?"production":void 0)!=="production"&&!es(_)&&console.warn("setSelf function cannot be used with read-only atom"),!U&&es(_)&&(U=(...b)=>{if((qn?"production":void 0)!=="production"&&O&&console.warn("setSelf function cannot be called in sync"),!O)try{return f(_,...b)}finally{w(),y()}}),U}},$=E.n;try{const b=s(_,D,j);return ts(_,b,h),xu(b)&&((P=b.onCancel)==null||P.call(b,()=>k==null?void 0:k.abort()),b.then(R,R)),E}catch(b){return delete E.v,E.e=b,++E.n,E}finally{O=!1,$!==E.n&&n.get(_)===$&&(n.set(_,E.n),r.add(_),(z=l.c)==null||z.call(l,_))}}),S=m[4]||(_=>{const P=[_];for(;P.length;){const z=P.pop(),E=h(z);for(const O of nd(z,E,t)){const R=h(O);n.set(O,R.n),P.push(O)}}}),f=m[5]||((_,...P)=>{let z=!0;const E=R=>Jo(p(R)),O=(R,...D)=>{var k;const U=h(R);try{if(qc(_,R)){if(!ed(R))throw new Error("atom not writable");const j=U.n,$=D[0];ts(R,$,h),c(R),j!==U.n&&(r.add(R),(k=l.c)==null||k.call(l,R),S(R));return}else return f(R,...D)}finally{z||(w(),y())}};try{return a(_,E,O,...P)}finally{z=!1}}),c=m[6]||(_=>{var P;const z=h(_),E=t.get(_);if(E&&!ua(z.v)){for(const[O,R]of z.d)if(!E.d.has(O)){const D=h(O);v(O).t.add(_),E.d.add(O),R!==D.n&&(r.add(O),(P=l.c)==null||P.call(l,O),S(O))}for(const O of E.d||[])if(!z.d.has(O)){E.d.delete(O);const R=x(O);R==null||R.t.delete(_)}}}),v=m[7]||(_=>{var P;const z=h(_);let E=t.get(_);if(!E){p(_);for(const O of z.d.keys())v(O).t.add(_);if(E={l:new Set,d:new Set(z.d.keys()),t:new Set},t.set(_,E),(P=l.m)==null||P.call(l,_),es(_)){const O=()=>{let R=!0;const D=(...k)=>{try{return f(_,...k)}finally{R||(w(),y())}};try{const k=d(_,D);k&&(E.u=()=>{R=!0;try{k()}finally{R=!1}})}finally{R=!1}};o.add(O)}}return E}),x=m[8]||(_=>{var P;const z=h(_);let E=t.get(_);if(E&&!E.l.size&&!Array.from(E.t).some(O=>{var R;return(R=t.get(O))==null?void 0:R.d.has(_)})){E.u&&i.add(E.u),E=void 0,t.delete(_),(P=l.u)==null||P.call(l,_);for(const O of z.d.keys()){const R=x(O);R==null||R.t.delete(_)}return}return E}),C=[e,t,n,r,o,i,l,s,a,u,d,h,y,w,p,S,f,c,v,x],A={get:_=>Jo(p(_)),set:(_,...P)=>{try{return f(_,...P)}finally{w(),y()}},sub:(_,P)=>{const E=v(_).l;return E.add(P),y(),()=>{E.delete(P),x(_),y()}}};return Object.defineProperty(A,$y,{value:C}),A},eh=Vy,Hy=By;var _u={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};let Wy=0;function Ky(e,t){const n=`atom${++Wy}`,r={toString(){return(_u?"production":void 0)!=="production"&&this.debugLabel?n+":"+this.debugLabel:n}};return typeof e=="function"?r.read=e:(r.init=e,r.read=Gy,r.write=Qy),r}function Gy(e){return e(this)}function Qy(e,t,n){return t(this,typeof n=="function"?n(e(this)):n)}const Yy=()=>{let e=0;const t=Hy({}),n=new WeakMap,r=new WeakMap,o=eh(n,r,void 0,void 0,void 0,void 0,t,void 0,(s,a,u,...d)=>e?u(s,...d):s.write(a,u,...d)),i=new Set;return t.m.add(void 0,s=>{i.add(s);const a=n.get(s);a.m=r.get(s)}),t.u.add(void 0,s=>{i.delete(s);const a=n.get(s);delete a.m}),Object.assign(o,{dev4_get_internal_weak_map:()=>n,dev4_get_mounted_atoms:()=>i,dev4_restore_atoms:s=>{const a={read:()=>null,write:(u,d)=>{++e;try{for(const[m,h]of s)"init"in m&&d(m,h)}finally{--e}}};o.set(a)}})},Xy=()=>(_u?"production":void 0)!=="production"?Yy():eh();let Ur;const Zy=()=>(Ur||(Ur=Xy(),(_u?"production":void 0)!=="production"&&(globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=Ur),globalThis.__JOTAI_DEFAULT_STORE__!==Ur&&console.warn("Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044"))),Ur);var th={BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const Jy=g.createContext(void 0),nh=e=>g.useContext(Jy)||Zy(),rh=e=>typeof(e==null?void 0:e.then)=="function",qy=e=>{e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t})},ew=Nt.use||(e=>{if(e.status==="pending")throw e;if(e.status==="fulfilled")return e.value;throw e.status==="rejected"?e.reason:(qy(e),e)}),rs=new WeakMap,tw=e=>{let t=rs.get(e);return t||(t=new Promise((n,r)=>{let o=e;const i=a=>u=>{o===a&&n(u)},l=a=>u=>{o===a&&r(u)},s=a=>{"onCancel"in a&&typeof a.onCancel=="function"&&a.onCancel(u=>{if((th?"production":void 0)!=="production"&&u===a)throw new Error("[Bug] p is not updated even after cancelation");rh(u)?(rs.set(u,t),o=u,u.then(i(u),l(u)),s(u)):n(u)})};e.then(i(e),l(e)),s(e)}),rs.set(e,t)),t};function nw(e,t){const n=nh(),[[r,o,i],l]=g.useReducer(u=>{const d=n.get(e);return Object.is(u[0],d)&&u[1]===n&&u[2]===e?u:[d,n,e]},void 0,()=>[n.get(e),n,e]);let s=r;if((o!==n||i!==e)&&(l(),s=n.get(e)),g.useEffect(()=>{const u=n.sub(e,()=>{l()});return l(),u},[n,e,void 0]),g.useDebugValue(s),rh(s)){const u=tw(s);return ew(u)}return s}function oh(e,t){const n=nh();return g.useCallback((...o)=>{if((th?"production":void 0)!=="production"&&!("write"in e))throw new Error("not writable atom");return n.set(e,...o)},[n,e])}function rw(e,t){return[nw(e),oh(e)]}const ih=Ky([]);function Ax(){const{addresses:e,gameState:t,gameData:n,isLoadingGameData:r}=lw(),[o,i]=rw(ih);return{logs:o,addLogItem:async y=>{if(!e||y.commandId!==void 0&&y.commandId==255)return;const w=await wi(e.battle_obj_ptr,ro.Int),p=await Ay(w,612),S=new DataView(new Uint8Array(p).buffer),f=S.getInt32(532,!0),c=S.getUint8(536)&1,v=S.getUint8(544)&2,x={...y,timestamp:Date.now(),damage:f,miss:!!c,crit:!!v};i(C=>C.some(_=>_.queuePosition!==void 0&&_.priority!==void 0&&x.queuePosition!==void 0&&x.priority!==void 0&&_.queuePosition===x.queuePosition&&_.priority===x.priority)?C:(console.debug("Adding new battle log item",x),[...C,x]))},hasLogItem:(y,w)=>o.some(p=>p.queuePosition!==void 0&&p.priority!==void 0&&p.queuePosition===y&&p.priority===w),formatCommand:(y,w)=>{if(y===void 0||w===void 0)return"Unknown Event";if(r)return"Loading names...";const p=n.commandNames,S=n.magicNames,f=n.summonNames,c=n.itemNames,v=n.enemySkillNames,x=n.enemyAttackNames,C=p[y]||`Unknown Cmd ${y.toString(16).toUpperCase()}`;switch(y){case 2:return`${S[w]||`Unknown Magic ${w}`}`;case 3:return`${f[w]||`Unknown Summon ${w}`}`;case 4:return`${c[w]||`Unknown Item ${w}`}`;case 13:return`${C}: ${v[w]||`Unknown E.Skill ${w}`}`;case 32:return x[w]||`Unknown Enemy Attack ${w.toString(16).toUpperCase()}`;case 35:return"Poison";default:return C}},getDamageType:(y,w)=>{if(y===void 0||w===void 0||r||!(n!=null&&n.itemData))return"HP";if(y===4){const p=n.itemData[w];if(p&&p.special_attack_flags&lh.DamageMP)return"MP"}return"HP"},isRestorative:(y,w)=>{if(y===void 0||w===void 0||r||!(n!=null&&n.itemData))return!1;if(y===4){const p=n.itemData[w];return p?(p.attack_element&1<<Zp.Restorative)!==0:!1}return!1},addStatusChangeItem:(y,w)=>{if(w.length===0)return;const p={targetCharacterIndex:y,changedStatuses:w,timestamp:Date.now()};console.debug("Adding status change log item",p),i(S=>[...S,p])},isLoadingNames:r,clearLogs:()=>{i([])}}}var lh=(e=>(e[e.DamageMP=1]="DamageMP",e[e.Unknown_2=2]="Unknown_2",e[e.AffectedByDarkness=4]="AffectedByDarkness",e[e.DrainsSomeHP=16]="DrainsSomeHP",e[e.DrainsSomeHPMP=32]="DrainsSomeHPMP",e))(lh||{});const ow={commandNames:[],magicNames:[],summonNames:[],enemySkillNames:[],enemyAttackNames:[],itemData:[],itemNames:[],materiaNames:[],battleScenes:[]},iw={currentModule:0,gameMoment:0,fieldId:0,fieldName:"",fieldFps:0,battleFps:0,worldFps:0,inGameTime:0,discId:0,menuVisibility:0,menuLocks:0,fieldMovementDisabled:0,fieldMenuAccessEnabled:0,fieldSkipDialoguesEnabled:!1,partyLockingBitmask:0,partyVisibilityBitmask:0,gil:0,gp:0,battleCount:0,battleEscapeCount:0,battlesDisabled:!1,maxBattlesEnabled:!1,gameObjPtr:0,battleSwirlDisabled:!1,instantATBEnabled:!1,manualSlotsEnabled:!1,slotsActive:0,fieldCurrentModelId:0,fieldModels:[],battleAllies:[],battleEnemies:[],speed:"",unfocusPatchEnabled:!1,isFFnx:!1,stepId:0,stepOffset:0,stepFraction:0,dangerValue:0,formationIndex:0,battleId:0,invincibilityEnabled:!1,worldCurrentModel:{},expMultiplier:1,apMultiplier:1,gilMultiplier:1,randomEncounters:oo.Normal,worldModels:[],battleChocoboRating:0,menuAlwaysEnabled:!1,worldZoomTiltEnabled:!1,worldZoom:0,worldTilt:0,worldSpeedMultiplier:2,partyMemberIds:[],keyItems:[],partyMembers:[],zolomCoords:null,worldMapType:0,fieldTmpVars:[],fieldLines:[],battleQueue:[],walkAnywhereEnabled:!1,lovePoints:[],battlePoints:0,chocoboData:null},sh=g.createContext(void 0),Tx=({children:e})=>{const{addresses:t,isLoading:n,error:r}=My(),o=oh(ih),[i,l]=g.useState(!1),[s,a]=g.useState(iw),[u,d]=g.useState(ow),[m,h]=g.useState(!0),[y,w]=g.useState({skipIntro:!1}),p=g.useRef({}),S=g.useRef(null),[f,c]=g.useState(null),[v,x]=g.useState(0),C=(E,O)=>{if(O.length===0)return;const R={targetCharacterIndex:E,changedStatuses:O,timestamp:Date.now()};console.debug("Adding status change log item (from context)",R),o(D=>[...D,R])},A=(E,O)=>{const R=p.current[O];if(!E||R!==void 0&&E.status===R)return;const D=E.status??0,k=R??0,U=[];for(const j in $e){const $=$e[j],b=(k&$)!==0,I=(D&$)!==0;b!==I&&U.push({statusId:j,inflicted:I})}U.length>0&&(console.log(`Status change detected for index ${O}:`,U),C(O,U))},_=async()=>{if(!(!i||s.currentModule===st.None))try{console.debug("Loading core game data...",s.currentModule),h(!0),c(null);const E=await T("read_command_names"),O=await T("read_attack_names"),R=await T("read_item_data"),D=await T("read_item_names"),k=await T("read_materia_names"),U=await T("read_battle_scenes"),j=O.slice(0,56),$=O.slice(56,72),b=O.slice(72,96);d(I=>({...I,commandNames:E,magicNames:j,summonNames:$,enemySkillNames:b,itemData:R,itemNames:D,materiaNames:k,battleScenes:U})),c(null),console.debug("Core game data loaded.")}catch(E){c(E),console.error("Failed to load core game data:",E)}finally{h(!1)}};g.useEffect(()=>{if(!i)return;let E=null,O=!1;return(async()=>{await _(),!O&&i&&f&&(E=setTimeout(()=>{x(D=>D+1)},5e3))})(),()=>{O=!0,E&&clearTimeout(E)}},[i,v]),g.useEffect(()=>{i||(x(0),c(null))},[i]),g.useEffect(()=>{if(n||r||!t){l(!1),p.current={};return}const E=D=>{const k=[];for(let U=0;U<64;U++)D[Math.floor(U/8)]&1<<U%8&&k.push(U);return k},O=async()=>{if(!t){l(!1),p.current={};return}try{const D=await T("read_ff7_data"),k=D.basic,U=k.current_module;U===st.Battle&&S.current!==st.Battle&&(console.debug("Transitioned into Battle module. Clearing previous statuses ref."),p.current={});const j=D.field_data,$=k.ffnx_check===233,b=k.current_module===st.Field?k.field_fps:k.current_module===st.Battle?k.battle_fps:k.world_fps,I=k.current_module===st.Field?30:k.current_module===st.Battle?15:30;let F=Math.floor(1e7/b/I*100)/100;if(Math.round(F*10)===10&&(F=1),$){if(!t){l(!1);return}try{const G=await wi(t.ffnx_check+1,ro.Int)+t.ffnx_check+5,W=await wi(G+10,ro.Int),H=await wi(W,ro.Float);F=Math.floor(H/30*100)/100}catch(G){console.warn("Failed to read FFnx speed:",G)}}let B=j.field_name.map(G=>String.fromCharCode(G)).join("");B=B.split("\\")[0],(!B||B.length===0)&&(B="N/A");const q=D.field_data.field_model_names.map((G,W)=>{if(!D.field_models||D.field_models.length===0||W>=D.field_models.length)return null;const H=D.field_models[W],X=typeof G=="string"?G:Array.isArray(G)?String.fromCharCode(...G.filter(Je=>Je!==0)):"";return{name:(X.startsWith(B+"_")?X.substring(B.length+1):X).split(".")[0],x:H.x,y:H.y,z:H.z,direction:H.direction,triangle:H.triangle,collision:H.collision?0:1,interaction:H.interaction?0:1,visible:H.visible}}).filter(G=>G!==null),Ee=D.world_current_model;Ee.script=Ee.walkmesh_type>>5,Ee.walkmesh_type=Ee.walkmesh_type&31;const ve=k.field_battle_check===188649,Ze=k.field_battle_check===2425393296;let he=oo.Normal;ve?he=oo.Off:Ze&&(he=oo.Max);const V=D.world_models;let J=null;try{J=await T("read_chocobo_data")}catch(G){console.warn("Failed to load chocobo data:",G)}a(G=>({...G,currentModule:k.current_module,gameMoment:k.game_moment,fieldId:k.field_id,fieldCurrentModelId:k.field_current_model_id,fieldName:B,fieldFps:k.field_fps,battleFps:k.battle_fps,worldFps:k.world_fps,inGameTime:k.in_game_time,discId:k.disc_id,menuVisibility:k.menu_visibility,menuLocks:k.menu_locks,fieldMovementDisabled:k.field_movement_disabled,fieldMenuAccessEnabled:k.field_menu_access_enabled,fieldSkipDialoguesEnabled:k.field_skip_dialogues_check!==139,partyLockingBitmask:k.party_locking_mask,partyVisibilityBitmask:k.party_visibility_mask,gil:k.gil,gp:k.gp,battleCount:k.battle_count,battleEscapeCount:k.battle_escape_count,battlesDisabled:k.field_battle_check===188649,maxBattlesEnabled:k.field_battle_check===2425393296,randomEncounters:he,gameObjPtr:k.game_obj_ptr,battleSwirlDisabled:k.battle_swirl_check===0,instantATBEnabled:k.instant_atb_check===17863,manualSlotsEnabled:k.manual_slots_check===0,slotsActive:k.slots_active,speed:""+F,unfocusPatchEnabled:k.unfocus_patch_check===128,isFFnx:$,stepId:k.step_id,stepOffset:k.step_offset,stepFraction:k.step_fraction,dangerValue:k.danger_value,formationIndex:k.formation_idx,battleId:k.battle_id,fieldModels:q,battleAllies:D.battle_allies,battleEnemies:D.battle_enemies,invincibilityEnabled:k.invincibility_check!==20200,worldCurrentModel:Ee,expMultiplier:k.exp_multiplier!==56?k.exp_multiplier:1,gilMultiplier:k.gil_multiplier!==177?k.gil_multiplier:1,apMultiplier:k.ap_multiplier!==226?k.ap_multiplier:1,worldModels:V,battleChocoboRating:k.battle_chocobo_rating,menuAlwaysEnabled:k.menu_always_enabled===199,worldZoomTiltEnabled:k.world_zoom_tilt_enabled===1,worldZoom:k.world_zoom,worldTilt:k.world_tilt,worldSpeedMultiplier:k.world_speed_multiplier,partyMemberIds:k.party_member_ids,keyItems:E(k.key_items),partyMembers:D.party_members,zolomCoords:k.zolom_coords?[k.zolom_coords>>16,k.zolom_coords&65535]:null,worldMapType:k.world_map_type,fieldTmpVars:k.field_tmp_vars,fieldLines:D.field_lines,battleQueue:k.battle_queue,walkAnywhereEnabled:k.walk_anywhere_check===233,lovePoints:k.love_points,battlePoints:k.battle_points,chocoboData:J})),setTimeout(()=>{const G={};if(k.current_module===st.Battle){for(let W=0;W<4;W++){const H=D.battle_allies[W];A(H,W),H&&(G[W]=H.status)}for(let W=0;W<6;W++){const H=D.battle_enemies[W];A(H,W+4),H&&(G[W+4]=H.status)}p.current=G}else Object.keys(p.current).length>0&&(p.current={})},50),S.current=U,l(U!==st.None)}catch(D){console.warn("Could not read FF7 data: ",D),l(!1)}};O();const R=setInterval(O,125);return()=>clearInterval(R)},[t,n,r,i,s.currentModule]);const P=async()=>{if(i)try{h(!0);const E=await T("read_enemy_attack_names");d(O=>({...O,enemyAttackNames:E}))}catch(E){console.error("Failed to load enemy attack names:",E)}finally{h(!1)}};if(g.useEffect(()=>{i&&s.currentModule===st.Battle&&![0,65535].includes(s.battleId)&&P()},[i,s.currentModule,s.battleId]),n)return N.jsx("div",{className:"flex items-center justify-center h-screen",children:"Loading addresses..."});if(r)return N.jsxs("div",{className:"flex items-center justify-center h-screen text-red-500",children:["Error loading FF7 addresses: ",r.toString()]});if(!t)return N.jsx("div",{className:"flex items-center justify-center h-screen",children:"FF7 addresses not available. Is the game running?"});const z={connected:i,gameState:s,gameData:u,isLoadingGameData:m,hacks:y,setHacks:w,addresses:t,isLoadingAddresses:!1,errorAddresses:null};return N.jsx(sh.Provider,{value:z,children:e})},lw=()=>{const e=g.useContext(sh);if(e===void 0)throw new Error("useFF7Context must be used within a FF7Provider");return e};var xe;(function(e){e.WINDOW_RESIZED="tauri://resize",e.WINDOW_MOVED="tauri://move",e.WINDOW_CLOSE_REQUESTED="tauri://close-requested",e.WINDOW_DESTROYED="tauri://destroyed",e.WINDOW_FOCUS="tauri://focus",e.WINDOW_BLUR="tauri://blur",e.WINDOW_SCALE_FACTOR_CHANGED="tauri://scale-change",e.WINDOW_THEME_CHANGED="tauri://theme-changed",e.WINDOW_CREATED="tauri://window-created",e.WEBVIEW_CREATED="tauri://webview-created",e.DRAG_ENTER="tauri://drag-enter",e.DRAG_OVER="tauri://drag-over",e.DRAG_DROP="tauri://drag-drop",e.DRAG_LEAVE="tauri://drag-leave"})(xe||(xe={}));async function ah(e,t){await T("plugin:event|unlisten",{event:e,eventId:t})}async function Cu(e,t,n){var r;const o=typeof(n==null?void 0:n.target)=="string"?{kind:"AnyLabel",label:n.target}:(r=n==null?void 0:n.target)!==null&&r!==void 0?r:{kind:"Any"};return T("plugin:event|listen",{event:e,target:o,handler:Yp(t)}).then(i=>async()=>ah(e,i))}async function uh(e,t,n){return Cu(e,r=>{ah(e,r.id),t(r)},n)}async function ch(e,t){await T("plugin:event|emit",{event:e,payload:t})}async function dh(e,t,n){await T("plugin:event|emit_to",{target:typeof e=="string"?{kind:"AnyLabel",label:e}:e,event:t,payload:n})}function rd(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function fh(...e){return t=>{let n=!1;const r=e.map(o=>{const i=rd(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():rd(e[o],null)}}}}function pe(...e){return g.useCallback(fh(...e),e)}var yr=g.forwardRef((e,t)=>{const{children:n,...r}=e,o=g.Children.toArray(n),i=o.find(sw);if(i){const l=i.props.children,s=o.map(a=>a===i?g.Children.count(l)>1?g.Children.only(null):g.isValidElement(l)?l.props.children:null:a);return N.jsx(ca,{...r,ref:t,children:g.isValidElement(l)?g.cloneElement(l,void 0,s):null})}return N.jsx(ca,{...r,ref:t,children:n})});yr.displayName="Slot";var ca=g.forwardRef((e,t)=>{const{children:n,...r}=e;if(g.isValidElement(n)){const o=uw(n);return g.cloneElement(n,{...aw(r,n.props),ref:t?fh(t,o):o})}return g.Children.count(n)>1?g.Children.only(null):null});ca.displayName="SlotClone";var ph=({children:e})=>N.jsx(N.Fragment,{children:e});function sw(e){return g.isValidElement(e)&&e.type===ph}function aw(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...s)=>{i(...s),o(...s)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function uw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function hh(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=hh(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function mh(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=hh(e))&&(r&&(r+=" "),r+=t);return r}const od=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,id=mh,cw=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return id(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,l=Object.keys(o).map(u=>{const d=n==null?void 0:n[u],m=i==null?void 0:i[u];if(d===null)return null;const h=od(d)||od(m);return o[u][h]}),s=n&&Object.entries(n).reduce((u,d)=>{let[m,h]=d;return h===void 0||(u[m]=h),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,d)=>{let{class:m,className:h,...y}=d;return Object.entries(y).every(w=>{let[p,S]=w;return Array.isArray(S)?S.includes({...i,...s}[p]):{...i,...s}[p]===S})?[...u,m,h]:u},[]);return id(e,l,a,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),gh=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var fw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pw=g.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:l,...s},a)=>g.createElement("svg",{ref:a,...fw,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:gh("lucide",o),...s},[...l.map(([u,d])=>g.createElement(u,d)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eu=(e,t)=>{const n=g.forwardRef(({className:r,...o},i)=>g.createElement(pw,{ref:i,iconNode:t,className:gh(`lucide-${dw(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hw=Eu("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vh=Eu("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.468.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mw=Eu("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),ku="-",gw=e=>{const t=yw(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:l=>{const s=l.split(ku);return s[0]===""&&s.length!==1&&s.shift(),yh(s,t)||vw(l)},getConflictingClassGroupIds:(l,s)=>{const a=n[l]||[];return s&&r[l]?[...a,...r[l]]:a}}},yh=(e,t)=>{var l;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?yh(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(ku);return(l=t.validators.find(({validator:s})=>s(i)))==null?void 0:l.classGroupId},ld=/^\[(.+)\]$/,vw=e=>{if(ld.test(e)){const t=ld.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},yw=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Sw(Object.entries(e.classGroups),n).forEach(([i,l])=>{da(l,r,i,t)}),r},da=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:sd(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(ww(o)){da(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,l])=>{da(l,sd(t,i),n,r)})})},sd=(e,t)=>{let n=e;return t.split(ku).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},ww=e=>e.isThemeGetter,Sw=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([l,s])=>[t+l,s])):i);return[n,o]}):e,xw=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,l)=>{n.set(i,l),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let l=n.get(i);if(l!==void 0)return l;if((l=r.get(i))!==void 0)return o(i,l),l},set(i,l){n.has(i)?n.set(i,l):o(i,l)}}},wh="!",_w=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,l=s=>{const a=[];let u=0,d=0,m;for(let S=0;S<s.length;S++){let f=s[S];if(u===0){if(f===o&&(r||s.slice(S,S+i)===t)){a.push(s.slice(d,S)),d=S+i;continue}if(f==="/"){m=S;continue}}f==="["?u++:f==="]"&&u--}const h=a.length===0?s:s.substring(d),y=h.startsWith(wh),w=y?h.substring(1):h,p=m&&m>d?m-d:void 0;return{modifiers:a,hasImportantModifier:y,baseClassName:w,maybePostfixModifierPosition:p}};return n?s=>n({className:s,parseClassName:l}):l},Cw=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Ew=e=>({cache:xw(e.cacheSize),parseClassName:_w(e),...gw(e)}),kw=/\s+/,bw=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],l=e.trim().split(kw);let s="";for(let a=l.length-1;a>=0;a-=1){const u=l[a],{modifiers:d,hasImportantModifier:m,baseClassName:h,maybePostfixModifierPosition:y}=n(u);let w=!!y,p=r(w?h.substring(0,y):h);if(!p){if(!w){s=u+(s.length>0?" "+s:s);continue}if(p=r(h),!p){s=u+(s.length>0?" "+s:s);continue}w=!1}const S=Cw(d).join(":"),f=m?S+wh:S,c=f+p;if(i.includes(c))continue;i.push(c);const v=o(p,w);for(let x=0;x<v.length;++x){const C=v[x];i.push(f+C)}s=u+(s.length>0?" "+s:s)}return s};function Pw(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Sh(t))&&(r&&(r+=" "),r+=n);return r}const Sh=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Sh(e[r]))&&(n&&(n+=" "),n+=t);return n};function Rw(e,...t){let n,r,o,i=l;function l(a){const u=t.reduce((d,m)=>m(d),e());return n=Ew(u),r=n.cache.get,o=n.cache.set,i=s,s(a)}function s(a){const u=r(a);if(u)return u;const d=bw(a,n);return o(a,d),d}return function(){return i(Pw.apply(null,arguments))}}const ne=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},xh=/^\[(?:([a-z-]+):)?(.+)\]$/i,Nw=/^\d+\/\d+$/,Aw=new Set(["px","full","screen"]),Tw=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ow=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Lw=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Dw=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Iw=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Rt=e=>sr(e)||Aw.has(e)||Nw.test(e),Ht=e=>kr(e,"length",Vw),sr=e=>!!e&&!Number.isNaN(Number(e)),os=e=>kr(e,"number",sr),Br=e=>!!e&&Number.isInteger(Number(e)),zw=e=>e.endsWith("%")&&sr(e.slice(0,-1)),K=e=>xh.test(e),Wt=e=>Tw.test(e),Mw=new Set(["length","size","percentage"]),Fw=e=>kr(e,Mw,_h),jw=e=>kr(e,"position",_h),Uw=new Set(["image","url"]),Bw=e=>kr(e,Uw,Ww),$w=e=>kr(e,"",Hw),$r=()=>!0,kr=(e,t,n)=>{const r=xh.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Vw=e=>Ow.test(e)&&!Lw.test(e),_h=()=>!1,Hw=e=>Dw.test(e),Ww=e=>Iw.test(e),Kw=()=>{const e=ne("colors"),t=ne("spacing"),n=ne("blur"),r=ne("brightness"),o=ne("borderColor"),i=ne("borderRadius"),l=ne("borderSpacing"),s=ne("borderWidth"),a=ne("contrast"),u=ne("grayscale"),d=ne("hueRotate"),m=ne("invert"),h=ne("gap"),y=ne("gradientColorStops"),w=ne("gradientColorStopPositions"),p=ne("inset"),S=ne("margin"),f=ne("opacity"),c=ne("padding"),v=ne("saturate"),x=ne("scale"),C=ne("sepia"),A=ne("skew"),_=ne("space"),P=ne("translate"),z=()=>["auto","contain","none"],E=()=>["auto","hidden","clip","visible","scroll"],O=()=>["auto",K,t],R=()=>[K,t],D=()=>["",Rt,Ht],k=()=>["auto",sr,K],U=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],j=()=>["solid","dashed","dotted","double","none"],$=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],b=()=>["start","end","center","between","around","evenly","stretch"],I=()=>["","0",K],F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>[sr,K];return{cacheSize:500,separator:":",theme:{colors:[$r],spacing:[Rt,Ht],blur:["none","",Wt,K],brightness:B(),borderColor:[e],borderRadius:["none","","full",Wt,K],borderSpacing:R(),borderWidth:D(),contrast:B(),grayscale:I(),hueRotate:B(),invert:I(),gap:R(),gradientColorStops:[e],gradientColorStopPositions:[zw,Ht],inset:O(),margin:O(),opacity:B(),padding:R(),saturate:B(),scale:B(),sepia:I(),skew:B(),space:R(),translate:R()},classGroups:{aspect:[{aspect:["auto","square","video",K]}],container:["container"],columns:[{columns:[Wt]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...U(),K]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[p]}],"inset-x":[{"inset-x":[p]}],"inset-y":[{"inset-y":[p]}],start:[{start:[p]}],end:[{end:[p]}],top:[{top:[p]}],right:[{right:[p]}],bottom:[{bottom:[p]}],left:[{left:[p]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Br,K]}],basis:[{basis:O()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",K]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",Br,K]}],"grid-cols":[{"grid-cols":[$r]}],"col-start-end":[{col:["auto",{span:["full",Br,K]},K]}],"col-start":[{"col-start":k()}],"col-end":[{"col-end":k()}],"grid-rows":[{"grid-rows":[$r]}],"row-start-end":[{row:["auto",{span:[Br,K]},K]}],"row-start":[{"row-start":k()}],"row-end":[{"row-end":k()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",K]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",K]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...b()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...b(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...b(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[c]}],px:[{px:[c]}],py:[{py:[c]}],ps:[{ps:[c]}],pe:[{pe:[c]}],pt:[{pt:[c]}],pr:[{pr:[c]}],pb:[{pb:[c]}],pl:[{pl:[c]}],m:[{m:[S]}],mx:[{mx:[S]}],my:[{my:[S]}],ms:[{ms:[S]}],me:[{me:[S]}],mt:[{mt:[S]}],mr:[{mr:[S]}],mb:[{mb:[S]}],ml:[{ml:[S]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",K,t]}],"min-w":[{"min-w":[K,t,"min","max","fit"]}],"max-w":[{"max-w":[K,t,"none","full","min","max","fit","prose",{screen:[Wt]},Wt]}],h:[{h:[K,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[K,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[K,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[K,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Wt,Ht]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",os]}],"font-family":[{font:[$r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",K]}],"line-clamp":[{"line-clamp":["none",sr,os]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Rt,K]}],"list-image":[{"list-image":["none",K]}],"list-style-type":[{list:["none","disc","decimal",K]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[f]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...j(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Rt,Ht]}],"underline-offset":[{"underline-offset":["auto",Rt,K]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",K]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",K]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[f]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...U(),jw]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Fw]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Bw]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[f]}],"border-style":[{border:[...j(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[f]}],"divide-style":[{divide:j()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...j()]}],"outline-offset":[{"outline-offset":[Rt,K]}],"outline-w":[{outline:[Rt,Ht]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:D()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[f]}],"ring-offset-w":[{"ring-offset":[Rt,Ht]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Wt,$w]}],"shadow-color":[{shadow:[$r]}],opacity:[{opacity:[f]}],"mix-blend":[{"mix-blend":[...$(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":$()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",Wt,K]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[m]}],saturate:[{saturate:[v]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[f]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[l]}],"border-spacing-x":[{"border-spacing-x":[l]}],"border-spacing-y":[{"border-spacing-y":[l]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",K]}],duration:[{duration:B()}],ease:[{ease:["linear","in","out","in-out",K]}],delay:[{delay:B()}],animate:[{animate:["none","spin","ping","pulse","bounce",K]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[Br,K]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[A]}],"skew-y":[{"skew-y":[A]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",K]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",K]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",K]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Rt,Ht,os]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Gw=Rw(Kw);function kt(...e){return Gw(mh(e))}function Ox(e){const t={ARROWUP:"↑",ARROWDOWN:"↓",ARROWLEFT:"←",ARROWRIGHT:"→",ENTER:"⏎",ESCAPE:"Esc",BACKSPACE:"⌫",DELETE:"Del",SPACE:"Space",TAB:"⇥",CONTROL:"Ctrl",ALT:"Alt",SHIFT:"⇧",META:"⌘"};return e.includes("+")?e.split("+").map(n=>n==="Ctrl"||n==="Alt"||n==="Shift"?n:t[n]||n).join("+"):t[e]||e}const Qw=cw("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",xs:"h-4 rounded-md px-3 text-[9px] px-1 py-0",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),Yw=g.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const l=r?yr:"button";return N.jsx(l,{className:kt(Qw({variant:t,size:n,className:e})),ref:i,...o})});Yw.displayName="Button";var Xw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],de=Xw.reduce((e,t)=>{const n=g.forwardRef((r,o)=>{const{asChild:i,...l}=r,s=i?yr:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),N.jsx(s,{...l,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function Zw(e,t){e&&Er.flushSync(()=>e.dispatchEvent(t))}function ee(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Lx(e,t){const n=g.createContext(t),r=i=>{const{children:l,...s}=i,a=g.useMemo(()=>s,Object.values(s));return N.jsx(n.Provider,{value:a,children:l})};r.displayName=e+"Provider";function o(i){const l=g.useContext(n);if(l)return l;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function ml(e,t=[]){let n=[];function r(i,l){const s=g.createContext(l),a=n.length;n=[...n,l];const u=m=>{var f;const{scope:h,children:y,...w}=m,p=((f=h==null?void 0:h[e])==null?void 0:f[a])||s,S=g.useMemo(()=>w,Object.values(w));return N.jsx(p.Provider,{value:S,children:y})};u.displayName=i+"Provider";function d(m,h){var p;const y=((p=h==null?void 0:h[e])==null?void 0:p[a])||s,w=g.useContext(y);if(w)return w;if(l!==void 0)return l;throw new Error(`\`${m}\` must be used within \`${i}\``)}return[u,d]}const o=()=>{const i=n.map(l=>g.createContext(l));return function(s){const a=(s==null?void 0:s[e])||i;return g.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return o.scopeName=e,[r,Jw(o,...t)]}function Jw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const l=r.reduce((s,{useScope:a,scopeName:u})=>{const m=a(i)[`__scope${u}`];return{...s,...m}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}var Te=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},qw=yg.useId||(()=>{}),e0=0;function gl(e){const[t,n]=g.useState(qw());return Te(()=>{e||n(r=>r??String(e0++))},[e]),e||(t?`radix-${t}`:"")}function jt(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function fa({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=t0({defaultProp:t,onChange:n}),i=e!==void 0,l=i?e:r,s=jt(n),a=g.useCallback(u=>{if(i){const m=typeof u=="function"?u(e):u;m!==e&&s(m)}else o(u)},[i,e,o,s]);return[l,a]}function t0({defaultProp:e,onChange:t}){const n=g.useState(e),[r]=n,o=g.useRef(r),i=jt(t);return g.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}function n0(e,t=globalThis==null?void 0:globalThis.document){const n=jt(e);g.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var r0="DismissableLayer",pa="dismissableLayer.update",o0="dismissableLayer.pointerDownOutside",i0="dismissableLayer.focusOutside",ad,Ch=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),bu=g.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:l,onDismiss:s,...a}=e,u=g.useContext(Ch),[d,m]=g.useState(null),h=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=g.useState({}),w=pe(t,_=>m(_)),p=Array.from(u.layers),[S]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),f=p.indexOf(S),c=d?p.indexOf(d):-1,v=u.layersWithOutsidePointerEventsDisabled.size>0,x=c>=f,C=a0(_=>{const P=_.target,z=[...u.branches].some(E=>E.contains(P));!x||z||(o==null||o(_),l==null||l(_),_.defaultPrevented||s==null||s())},h),A=u0(_=>{const P=_.target;[...u.branches].some(E=>E.contains(P))||(i==null||i(_),l==null||l(_),_.defaultPrevented||s==null||s())},h);return n0(_=>{c===u.layers.size-1&&(r==null||r(_),!_.defaultPrevented&&s&&(_.preventDefault(),s()))},h),g.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(ad=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),ud(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=ad)}},[d,h,n,u]),g.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),ud())},[d,u]),g.useEffect(()=>{const _=()=>y({});return document.addEventListener(pa,_),()=>document.removeEventListener(pa,_)},[]),N.jsx(de.div,{...a,ref:w,style:{pointerEvents:v?x?"auto":"none":void 0,...e.style},onFocusCapture:ee(e.onFocusCapture,A.onFocusCapture),onBlurCapture:ee(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:ee(e.onPointerDownCapture,C.onPointerDownCapture)})});bu.displayName=r0;var l0="DismissableLayerBranch",s0=g.forwardRef((e,t)=>{const n=g.useContext(Ch),r=g.useRef(null),o=pe(t,r);return g.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),N.jsx(de.div,{...e,ref:o})});s0.displayName=l0;function a0(e,t=globalThis==null?void 0:globalThis.document){const n=jt(e),r=g.useRef(!1),o=g.useRef(()=>{});return g.useEffect(()=>{const i=s=>{if(s.target&&!r.current){let a=function(){Eh(o0,n,u,{discrete:!0})};const u={originalEvent:s};s.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},l=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(l),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function u0(e,t=globalThis==null?void 0:globalThis.document){const n=jt(e),r=g.useRef(!1);return g.useEffect(()=>{const o=i=>{i.target&&!r.current&&Eh(i0,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function ud(){const e=new CustomEvent(pa);document.dispatchEvent(e)}function Eh(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Zw(o,i):o.dispatchEvent(i)}var is="focusScope.autoFocusOnMount",ls="focusScope.autoFocusOnUnmount",cd={bubbles:!1,cancelable:!0},c0="FocusScope",kh=g.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...l}=e,[s,a]=g.useState(null),u=jt(o),d=jt(i),m=g.useRef(null),h=pe(t,p=>a(p)),y=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(r){let p=function(v){if(y.paused||!s)return;const x=v.target;s.contains(x)?m.current=x:Kt(m.current,{select:!0})},S=function(v){if(y.paused||!s)return;const x=v.relatedTarget;x!==null&&(s.contains(x)||Kt(m.current,{select:!0}))},f=function(v){if(document.activeElement===document.body)for(const C of v)C.removedNodes.length>0&&Kt(s)};document.addEventListener("focusin",p),document.addEventListener("focusout",S);const c=new MutationObserver(f);return s&&c.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",S),c.disconnect()}}},[r,s,y.paused]),g.useEffect(()=>{if(s){fd.add(y);const p=document.activeElement;if(!s.contains(p)){const f=new CustomEvent(is,cd);s.addEventListener(is,u),s.dispatchEvent(f),f.defaultPrevented||(d0(g0(bh(s)),{select:!0}),document.activeElement===p&&Kt(s))}return()=>{s.removeEventListener(is,u),setTimeout(()=>{const f=new CustomEvent(ls,cd);s.addEventListener(ls,d),s.dispatchEvent(f),f.defaultPrevented||Kt(p??document.body,{select:!0}),s.removeEventListener(ls,d),fd.remove(y)},0)}}},[s,u,d,y]);const w=g.useCallback(p=>{if(!n&&!r||y.paused)return;const S=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,f=document.activeElement;if(S&&f){const c=p.currentTarget,[v,x]=f0(c);v&&x?!p.shiftKey&&f===x?(p.preventDefault(),n&&Kt(v,{select:!0})):p.shiftKey&&f===v&&(p.preventDefault(),n&&Kt(x,{select:!0})):f===c&&p.preventDefault()}},[n,r,y.paused]);return N.jsx(de.div,{tabIndex:-1,...l,ref:h,onKeyDown:w})});kh.displayName=c0;function d0(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Kt(r,{select:t}),document.activeElement!==n)return}function f0(e){const t=bh(e),n=dd(t,e),r=dd(t.reverse(),e);return[n,r]}function bh(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function dd(e,t){for(const n of e)if(!p0(n,{upTo:t}))return n}function p0(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function h0(e){return e instanceof HTMLInputElement&&"select"in e}function Kt(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&h0(e)&&t&&e.select()}}var fd=m0();function m0(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=pd(e,t),e.unshift(t)},remove(t){var n;e=pd(e,t),(n=e[0])==null||n.resume()}}}function pd(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function g0(e){return e.filter(t=>t.tagName!=="A")}var v0="Portal",Pu=g.forwardRef((e,t)=>{var s;const{container:n,...r}=e,[o,i]=g.useState(!1);Te(()=>i(!0),[]);const l=n||o&&((s=globalThis==null?void 0:globalThis.document)==null?void 0:s.body);return l?Ny.createPortal(N.jsx(de.div,{...r,ref:t}),l):null});Pu.displayName=v0;function y0(e,t){return g.useReducer((n,r)=>t[n][r]??n,e)}var Ru=e=>{const{present:t,children:n}=e,r=w0(t),o=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),i=pe(r.ref,S0(o));return typeof n=="function"||r.isPresent?g.cloneElement(o,{ref:i}):null};Ru.displayName="Presence";function w0(e){const[t,n]=g.useState(),r=g.useRef({}),o=g.useRef(e),i=g.useRef("none"),l=e?"mounted":"unmounted",[s,a]=y0(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const u=qo(r.current);i.current=s==="mounted"?u:"none"},[s]),Te(()=>{const u=r.current,d=o.current;if(d!==e){const h=i.current,y=qo(u);e?a("MOUNT"):y==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(d&&h!==y?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),Te(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,m=y=>{const p=qo(r.current).includes(y.animationName);if(y.target===t&&p&&(a("ANIMATION_END"),!o.current)){const S=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=S)})}},h=y=>{y.target===t&&(i.current=qo(r.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:g.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function qo(e){return(e==null?void 0:e.animationName)||"none"}function S0(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var ss=0;function x0(){g.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??hd()),document.body.insertAdjacentElement("beforeend",e[1]??hd()),ss++,()=>{ss===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ss--}},[])}function hd(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var xt=function(){return xt=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},xt.apply(this,arguments)};function Ph(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function _0(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var Si="right-scroll-bar-position",xi="width-before-scroll-bar",C0="with-scroll-bars-hidden",E0="--removed-body-scroll-bar-size";function as(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function k0(e,t){var n=g.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var b0=typeof window<"u"?g.useLayoutEffect:g.useEffect,md=new WeakMap;function P0(e,t){var n=k0(null,function(r){return e.forEach(function(o){return as(o,r)})});return b0(function(){var r=md.get(n);if(r){var o=new Set(r),i=new Set(e),l=n.current;o.forEach(function(s){i.has(s)||as(s,null)}),i.forEach(function(s){o.has(s)||as(s,l)})}md.set(n,e)},[e]),n}function R0(e){return e}function N0(e,t){t===void 0&&(t=R0);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var l=t(i,r);return n.push(l),function(){n=n.filter(function(s){return s!==l})}},assignSyncMedium:function(i){for(r=!0;n.length;){var l=n;n=[],l.forEach(i)}n={push:function(s){return i(s)},filter:function(){return n}}},assignMedium:function(i){r=!0;var l=[];if(n.length){var s=n;n=[],s.forEach(i),l=n}var a=function(){var d=l;l=[],d.forEach(i)},u=function(){return Promise.resolve().then(a)};u(),n={push:function(d){l.push(d),u()},filter:function(d){return l=l.filter(d),n}}}};return o}function A0(e){e===void 0&&(e={});var t=N0(null);return t.options=xt({async:!0,ssr:!1},e),t}var Rh=function(e){var t=e.sideCar,n=Ph(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return g.createElement(r,xt({},n))};Rh.isSideCarExport=!0;function T0(e,t){return e.useMedium(t),Rh}var Nh=A0(),us=function(){},vl=g.forwardRef(function(e,t){var n=g.useRef(null),r=g.useState({onScrollCapture:us,onWheelCapture:us,onTouchMoveCapture:us}),o=r[0],i=r[1],l=e.forwardProps,s=e.children,a=e.className,u=e.removeScrollBar,d=e.enabled,m=e.shards,h=e.sideCar,y=e.noIsolation,w=e.inert,p=e.allowPinchZoom,S=e.as,f=S===void 0?"div":S,c=e.gapMode,v=Ph(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=h,C=P0([n,t]),A=xt(xt({},v),o);return g.createElement(g.Fragment,null,d&&g.createElement(x,{sideCar:Nh,removeScrollBar:u,shards:m,noIsolation:y,inert:w,setCallbacks:i,allowPinchZoom:!!p,lockRef:n,gapMode:c}),l?g.cloneElement(g.Children.only(s),xt(xt({},A),{ref:C})):g.createElement(f,xt({},A,{className:a,ref:C}),s))});vl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};vl.classNames={fullWidth:xi,zeroRight:Si};var O0=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function L0(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=O0();return t&&e.setAttribute("nonce",t),e}function D0(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function I0(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var z0=function(){var e=0,t=null;return{add:function(n){e==0&&(t=L0())&&(D0(t,n),I0(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},M0=function(){var e=z0();return function(t,n){g.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Ah=function(){var e=M0(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},F0={left:0,top:0,right:0,gap:0},cs=function(e){return parseInt(e||"",10)||0},j0=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[cs(n),cs(r),cs(o)]},U0=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return F0;var t=j0(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},B0=Ah(),ar="data-scroll-locked",$0=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,s=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(C0,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(s,"px ").concat(r,`;
  }
  body[`).concat(ar,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(l,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(s,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Si,` {
    right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(xi,` {
    margin-right: `).concat(s,"px ").concat(r,`;
  }
  
  .`).concat(Si," .").concat(Si,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(xi," .").concat(xi,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(ar,`] {
    `).concat(E0,": ").concat(s,`px;
  }
`)},gd=function(){var e=parseInt(document.body.getAttribute(ar)||"0",10);return isFinite(e)?e:0},V0=function(){g.useEffect(function(){return document.body.setAttribute(ar,(gd()+1).toString()),function(){var e=gd()-1;e<=0?document.body.removeAttribute(ar):document.body.setAttribute(ar,e.toString())}},[])},H0=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;V0();var i=g.useMemo(function(){return U0(o)},[o]);return g.createElement(B0,{styles:$0(i,!t,o,n?"":"!important")})},ha=!1;if(typeof window<"u")try{var ei=Object.defineProperty({},"passive",{get:function(){return ha=!0,!0}});window.addEventListener("test",ei,ei),window.removeEventListener("test",ei,ei)}catch{ha=!1}var Fn=ha?{passive:!1}:!1,W0=function(e){return e.tagName==="TEXTAREA"},Th=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!W0(e)&&n[t]==="visible")},K0=function(e){return Th(e,"overflowY")},G0=function(e){return Th(e,"overflowX")},vd=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Oh(e,r);if(o){var i=Lh(e,r),l=i[1],s=i[2];if(l>s)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Q0=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Y0=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Oh=function(e,t){return e==="v"?K0(t):G0(t)},Lh=function(e,t){return e==="v"?Q0(t):Y0(t)},X0=function(e,t){return e==="h"&&t==="rtl"?-1:1},Z0=function(e,t,n,r,o){var i=X0(e,window.getComputedStyle(t).direction),l=i*r,s=n.target,a=t.contains(s),u=!1,d=l>0,m=0,h=0;do{var y=Lh(e,s),w=y[0],p=y[1],S=y[2],f=p-S-i*w;(w||f)&&Oh(e,s)&&(m+=f,h+=w),s instanceof ShadowRoot?s=s.host:s=s.parentNode}while(!a&&s!==document.body||a&&(t.contains(s)||t===s));return(d&&(Math.abs(m)<1||!o)||!d&&(Math.abs(h)<1||!o))&&(u=!0),u},ti=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},yd=function(e){return[e.deltaX,e.deltaY]},wd=function(e){return e&&"current"in e?e.current:e},J0=function(e,t){return e[0]===t[0]&&e[1]===t[1]},q0=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},e1=0,jn=[];function t1(e){var t=g.useRef([]),n=g.useRef([0,0]),r=g.useRef(),o=g.useState(e1++)[0],i=g.useState(Ah)[0],l=g.useRef(e);g.useEffect(function(){l.current=e},[e]),g.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var p=_0([e.lockRef.current],(e.shards||[]).map(wd),!0).filter(Boolean);return p.forEach(function(S){return S.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),p.forEach(function(S){return S.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=g.useCallback(function(p,S){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!l.current.allowPinchZoom;var f=ti(p),c=n.current,v="deltaX"in p?p.deltaX:c[0]-f[0],x="deltaY"in p?p.deltaY:c[1]-f[1],C,A=p.target,_=Math.abs(v)>Math.abs(x)?"h":"v";if("touches"in p&&_==="h"&&A.type==="range")return!1;var P=vd(_,A);if(!P)return!0;if(P?C=_:(C=_==="v"?"h":"v",P=vd(_,A)),!P)return!1;if(!r.current&&"changedTouches"in p&&(v||x)&&(r.current=C),!C)return!0;var z=r.current||C;return Z0(z,S,p,z==="h"?v:x,!0)},[]),a=g.useCallback(function(p){var S=p;if(!(!jn.length||jn[jn.length-1]!==i)){var f="deltaY"in S?yd(S):ti(S),c=t.current.filter(function(C){return C.name===S.type&&(C.target===S.target||S.target===C.shadowParent)&&J0(C.delta,f)})[0];if(c&&c.should){S.cancelable&&S.preventDefault();return}if(!c){var v=(l.current.shards||[]).map(wd).filter(Boolean).filter(function(C){return C.contains(S.target)}),x=v.length>0?s(S,v[0]):!l.current.noIsolation;x&&S.cancelable&&S.preventDefault()}}},[]),u=g.useCallback(function(p,S,f,c){var v={name:p,delta:S,target:f,should:c,shadowParent:n1(f)};t.current.push(v),setTimeout(function(){t.current=t.current.filter(function(x){return x!==v})},1)},[]),d=g.useCallback(function(p){n.current=ti(p),r.current=void 0},[]),m=g.useCallback(function(p){u(p.type,yd(p),p.target,s(p,e.lockRef.current))},[]),h=g.useCallback(function(p){u(p.type,ti(p),p.target,s(p,e.lockRef.current))},[]);g.useEffect(function(){return jn.push(i),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:h}),document.addEventListener("wheel",a,Fn),document.addEventListener("touchmove",a,Fn),document.addEventListener("touchstart",d,Fn),function(){jn=jn.filter(function(p){return p!==i}),document.removeEventListener("wheel",a,Fn),document.removeEventListener("touchmove",a,Fn),document.removeEventListener("touchstart",d,Fn)}},[]);var y=e.removeScrollBar,w=e.inert;return g.createElement(g.Fragment,null,w?g.createElement(i,{styles:q0(o)}):null,y?g.createElement(H0,{gapMode:e.gapMode}):null)}function n1(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const r1=T0(Nh,t1);var Dh=g.forwardRef(function(e,t){return g.createElement(vl,xt({},e,{ref:t,sideCar:r1}))});Dh.classNames=vl.classNames;var o1=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Un=new WeakMap,ni=new WeakMap,ri={},ds=0,Ih=function(e){return e&&(e.host||Ih(e.parentNode))},i1=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Ih(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},l1=function(e,t,n,r){var o=i1(t,Array.isArray(e)?e:[e]);ri[n]||(ri[n]=new WeakMap);var i=ri[n],l=[],s=new Set,a=new Set(o),u=function(m){!m||s.has(m)||(s.add(m),u(m.parentNode))};o.forEach(u);var d=function(m){!m||a.has(m)||Array.prototype.forEach.call(m.children,function(h){if(s.has(h))d(h);else try{var y=h.getAttribute(r),w=y!==null&&y!=="false",p=(Un.get(h)||0)+1,S=(i.get(h)||0)+1;Un.set(h,p),i.set(h,S),l.push(h),p===1&&w&&ni.set(h,!0),S===1&&h.setAttribute(n,"true"),w||h.setAttribute(r,"true")}catch(f){console.error("aria-hidden: cannot operate on ",h,f)}})};return d(t),s.clear(),ds++,function(){l.forEach(function(m){var h=Un.get(m)-1,y=i.get(m)-1;Un.set(m,h),i.set(m,y),h||(ni.has(m)||m.removeAttribute(r),ni.delete(m)),y||m.removeAttribute(n)}),ds--,ds||(Un=new WeakMap,Un=new WeakMap,ni=new WeakMap,ri={})}},s1=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=o1(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),l1(r,o,n,"aria-hidden")):function(){return null}};const a1=["top","right","bottom","left"],dn=Math.min,He=Math.max,Gi=Math.round,oi=Math.floor,Et=e=>({x:e,y:e}),u1={left:"right",right:"left",bottom:"top",top:"bottom"},c1={start:"end",end:"start"};function ma(e,t,n){return He(e,dn(t,n))}function Ut(e,t){return typeof e=="function"?e(t):e}function Bt(e){return e.split("-")[0]}function br(e){return e.split("-")[1]}function Nu(e){return e==="x"?"y":"x"}function Au(e){return e==="y"?"height":"width"}function fn(e){return["top","bottom"].includes(Bt(e))?"y":"x"}function Tu(e){return Nu(fn(e))}function d1(e,t,n){n===void 0&&(n=!1);const r=br(e),o=Tu(e),i=Au(o);let l=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=Qi(l)),[l,Qi(l)]}function f1(e){const t=Qi(e);return[ga(e),t,ga(t)]}function ga(e){return e.replace(/start|end/g,t=>c1[t])}function p1(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],l=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:l;default:return[]}}function h1(e,t,n,r){const o=br(e);let i=p1(Bt(e),n==="start",r);return o&&(i=i.map(l=>l+"-"+o),t&&(i=i.concat(i.map(ga)))),i}function Qi(e){return e.replace(/left|right|bottom|top/g,t=>u1[t])}function m1(e){return{top:0,right:0,bottom:0,left:0,...e}}function zh(e){return typeof e!="number"?m1(e):{top:e,right:e,bottom:e,left:e}}function Yi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Sd(e,t,n){let{reference:r,floating:o}=e;const i=fn(t),l=Tu(t),s=Au(l),a=Bt(t),u=i==="y",d=r.x+r.width/2-o.width/2,m=r.y+r.height/2-o.height/2,h=r[s]/2-o[s]/2;let y;switch(a){case"top":y={x:d,y:r.y-o.height};break;case"bottom":y={x:d,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:m};break;case"left":y={x:r.x-o.width,y:m};break;default:y={x:r.x,y:r.y}}switch(br(t)){case"start":y[l]-=h*(n&&u?-1:1);break;case"end":y[l]+=h*(n&&u?-1:1);break}return y}const g1=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,s=i.filter(Boolean),a=await(l.isRTL==null?void 0:l.isRTL(t));let u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:m}=Sd(u,r,a),h=r,y={},w=0;for(let p=0;p<s.length;p++){const{name:S,fn:f}=s[p],{x:c,y:v,data:x,reset:C}=await f({x:d,y:m,initialPlacement:r,placement:h,strategy:o,middlewareData:y,rects:u,platform:l,elements:{reference:e,floating:t}});d=c??d,m=v??m,y={...y,[S]:{...y[S],...x}},C&&w<=50&&(w++,typeof C=="object"&&(C.placement&&(h=C.placement),C.rects&&(u=C.rects===!0?await l.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:d,y:m}=Sd(u,h,a)),p=-1)}return{x:d,y:m,placement:h,strategy:o,middlewareData:y}};async function Eo(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:l,elements:s,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:m="floating",altBoundary:h=!1,padding:y=0}=Ut(t,e),w=zh(y),S=s[h?m==="floating"?"reference":"floating":m],f=Yi(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:d,strategy:a})),c=m==="floating"?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,v=await(i.getOffsetParent==null?void 0:i.getOffsetParent(s.floating)),x=await(i.isElement==null?void 0:i.isElement(v))?await(i.getScale==null?void 0:i.getScale(v))||{x:1,y:1}:{x:1,y:1},C=Yi(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:c,offsetParent:v,strategy:a}):c);return{top:(f.top-C.top+w.top)/x.y,bottom:(C.bottom-f.bottom+w.bottom)/x.y,left:(f.left-C.left+w.left)/x.x,right:(C.right-f.right+w.right)/x.x}}const v1=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:l,elements:s,middlewareData:a}=t,{element:u,padding:d=0}=Ut(e,t)||{};if(u==null)return{};const m=zh(d),h={x:n,y:r},y=Tu(o),w=Au(y),p=await l.getDimensions(u),S=y==="y",f=S?"top":"left",c=S?"bottom":"right",v=S?"clientHeight":"clientWidth",x=i.reference[w]+i.reference[y]-h[y]-i.floating[w],C=h[y]-i.reference[y],A=await(l.getOffsetParent==null?void 0:l.getOffsetParent(u));let _=A?A[v]:0;(!_||!await(l.isElement==null?void 0:l.isElement(A)))&&(_=s.floating[v]||i.floating[w]);const P=x/2-C/2,z=_/2-p[w]/2-1,E=dn(m[f],z),O=dn(m[c],z),R=E,D=_-p[w]-O,k=_/2-p[w]/2+P,U=ma(R,k,D),j=!a.arrow&&br(o)!=null&&k!==U&&i.reference[w]/2-(k<R?E:O)-p[w]/2<0,$=j?k<R?k-R:k-D:0;return{[y]:h[y]+$,data:{[y]:U,centerOffset:k-U-$,...j&&{alignmentOffset:$}},reset:j}}}),y1=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:l,initialPlacement:s,platform:a,elements:u}=t,{mainAxis:d=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:p=!0,...S}=Ut(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const f=Bt(o),c=fn(s),v=Bt(s)===s,x=await(a.isRTL==null?void 0:a.isRTL(u.floating)),C=h||(v||!p?[Qi(s)]:f1(s)),A=w!=="none";!h&&A&&C.push(...h1(s,p,w,x));const _=[s,...C],P=await Eo(t,S),z=[];let E=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&z.push(P[f]),m){const k=d1(o,l,x);z.push(P[k[0]],P[k[1]])}if(E=[...E,{placement:o,overflows:z}],!z.every(k=>k<=0)){var O,R;const k=(((O=i.flip)==null?void 0:O.index)||0)+1,U=_[k];if(U)return{data:{index:k,overflows:E},reset:{placement:U}};let j=(R=E.filter($=>$.overflows[0]<=0).sort(($,b)=>$.overflows[1]-b.overflows[1])[0])==null?void 0:R.placement;if(!j)switch(y){case"bestFit":{var D;const $=(D=E.filter(b=>{if(A){const I=fn(b.placement);return I===c||I==="y"}return!0}).map(b=>[b.placement,b.overflows.filter(I=>I>0).reduce((I,F)=>I+F,0)]).sort((b,I)=>b[1]-I[1])[0])==null?void 0:D[0];$&&(j=$);break}case"initialPlacement":j=s;break}if(o!==j)return{reset:{placement:j}}}return{}}}};function xd(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function _d(e){return a1.some(t=>e[t]>=0)}const w1=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Ut(e,t);switch(r){case"referenceHidden":{const i=await Eo(t,{...o,elementContext:"reference"}),l=xd(i,n.reference);return{data:{referenceHiddenOffsets:l,referenceHidden:_d(l)}}}case"escaped":{const i=await Eo(t,{...o,altBoundary:!0}),l=xd(i,n.floating);return{data:{escapedOffsets:l,escaped:_d(l)}}}default:return{}}}}};async function S1(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),l=Bt(n),s=br(n),a=fn(n)==="y",u=["left","top"].includes(l)?-1:1,d=i&&a?-1:1,m=Ut(t,e);let{mainAxis:h,crossAxis:y,alignmentAxis:w}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return s&&typeof w=="number"&&(y=s==="end"?w*-1:w),a?{x:y*d,y:h*u}:{x:h*u,y:y*d}}const x1=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:l,middlewareData:s}=t,a=await S1(t,e);return l===((n=s.offset)==null?void 0:n.placement)&&(r=s.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:l}}}}},_1=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:s={fn:S=>{let{x:f,y:c}=S;return{x:f,y:c}}},...a}=Ut(e,t),u={x:n,y:r},d=await Eo(t,a),m=fn(Bt(o)),h=Nu(m);let y=u[h],w=u[m];if(i){const S=h==="y"?"top":"left",f=h==="y"?"bottom":"right",c=y+d[S],v=y-d[f];y=ma(c,y,v)}if(l){const S=m==="y"?"top":"left",f=m==="y"?"bottom":"right",c=w+d[S],v=w-d[f];w=ma(c,w,v)}const p=s.fn({...t,[h]:y,[m]:w});return{...p,data:{x:p.x-n,y:p.y-r,enabled:{[h]:i,[m]:l}}}}}},C1=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:s=0,mainAxis:a=!0,crossAxis:u=!0}=Ut(e,t),d={x:n,y:r},m=fn(o),h=Nu(m);let y=d[h],w=d[m];const p=Ut(s,t),S=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(a){const v=h==="y"?"height":"width",x=i.reference[h]-i.floating[v]+S.mainAxis,C=i.reference[h]+i.reference[v]-S.mainAxis;y<x?y=x:y>C&&(y=C)}if(u){var f,c;const v=h==="y"?"width":"height",x=["top","left"].includes(Bt(o)),C=i.reference[m]-i.floating[v]+(x&&((f=l.offset)==null?void 0:f[m])||0)+(x?0:S.crossAxis),A=i.reference[m]+i.reference[v]+(x?0:((c=l.offset)==null?void 0:c[m])||0)-(x?S.crossAxis:0);w<C?w=C:w>A&&(w=A)}return{[h]:y,[m]:w}}}},E1=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:l,elements:s}=t,{apply:a=()=>{},...u}=Ut(e,t),d=await Eo(t,u),m=Bt(o),h=br(o),y=fn(o)==="y",{width:w,height:p}=i.floating;let S,f;m==="top"||m==="bottom"?(S=m,f=h===(await(l.isRTL==null?void 0:l.isRTL(s.floating))?"start":"end")?"left":"right"):(f=m,S=h==="end"?"top":"bottom");const c=p-d.top-d.bottom,v=w-d.left-d.right,x=dn(p-d[S],c),C=dn(w-d[f],v),A=!t.middlewareData.shift;let _=x,P=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=v),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(_=c),A&&!h){const E=He(d.left,0),O=He(d.right,0),R=He(d.top,0),D=He(d.bottom,0);y?P=w-2*(E!==0||O!==0?E+O:He(d.left,d.right)):_=p-2*(R!==0||D!==0?R+D:He(d.top,d.bottom))}await a({...t,availableWidth:P,availableHeight:_});const z=await l.getDimensions(s.floating);return w!==z.width||p!==z.height?{reset:{rects:!0}}:{}}}};function yl(){return typeof window<"u"}function Pr(e){return Mh(e)?(e.nodeName||"").toLowerCase():"#document"}function Ge(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Pt(e){var t;return(t=(Mh(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Mh(e){return yl()?e instanceof Node||e instanceof Ge(e).Node:!1}function mt(e){return yl()?e instanceof Element||e instanceof Ge(e).Element:!1}function bt(e){return yl()?e instanceof HTMLElement||e instanceof Ge(e).HTMLElement:!1}function Cd(e){return!yl()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ge(e).ShadowRoot}function To(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=gt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function k1(e){return["table","td","th"].includes(Pr(e))}function wl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Ou(e){const t=Lu(),n=mt(e)?gt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function b1(e){let t=pn(e);for(;bt(t)&&!wr(t);){if(Ou(t))return t;if(wl(t))return null;t=pn(t)}return null}function Lu(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function wr(e){return["html","body","#document"].includes(Pr(e))}function gt(e){return Ge(e).getComputedStyle(e)}function Sl(e){return mt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function pn(e){if(Pr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Cd(e)&&e.host||Pt(e);return Cd(t)?t.host:t}function Fh(e){const t=pn(e);return wr(t)?e.ownerDocument?e.ownerDocument.body:e.body:bt(t)&&To(t)?t:Fh(t)}function ko(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Fh(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),l=Ge(o);if(i){const s=va(l);return t.concat(l,l.visualViewport||[],To(o)?o:[],s&&n?ko(s):[])}return t.concat(o,ko(o,[],n))}function va(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function jh(e){const t=gt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=bt(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=Gi(n)!==i||Gi(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function Du(e){return mt(e)?e:e.contextElement}function ur(e){const t=Du(e);if(!bt(t))return Et(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=jh(t);let l=(i?Gi(n.width):n.width)/r,s=(i?Gi(n.height):n.height)/o;return(!l||!Number.isFinite(l))&&(l=1),(!s||!Number.isFinite(s))&&(s=1),{x:l,y:s}}const P1=Et(0);function Uh(e){const t=Ge(e);return!Lu()||!t.visualViewport?P1:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function R1(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ge(e)?!1:t}function Tn(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Du(e);let l=Et(1);t&&(r?mt(r)&&(l=ur(r)):l=ur(e));const s=R1(i,n,r)?Uh(i):Et(0);let a=(o.left+s.x)/l.x,u=(o.top+s.y)/l.y,d=o.width/l.x,m=o.height/l.y;if(i){const h=Ge(i),y=r&&mt(r)?Ge(r):r;let w=h,p=va(w);for(;p&&r&&y!==w;){const S=ur(p),f=p.getBoundingClientRect(),c=gt(p),v=f.left+(p.clientLeft+parseFloat(c.paddingLeft))*S.x,x=f.top+(p.clientTop+parseFloat(c.paddingTop))*S.y;a*=S.x,u*=S.y,d*=S.x,m*=S.y,a+=v,u+=x,w=Ge(p),p=va(w)}}return Yi({width:d,height:m,x:a,y:u})}function Iu(e,t){const n=Sl(e).scrollLeft;return t?t.left+n:Tn(Pt(e)).left+n}function Bh(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Iu(e,r)),i=r.top+t.scrollTop;return{x:o,y:i}}function N1(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",l=Pt(r),s=t?wl(t.floating):!1;if(r===l||s&&i)return n;let a={scrollLeft:0,scrollTop:0},u=Et(1);const d=Et(0),m=bt(r);if((m||!m&&!i)&&((Pr(r)!=="body"||To(l))&&(a=Sl(r)),bt(r))){const y=Tn(r);u=ur(r),d.x=y.x+r.clientLeft,d.y=y.y+r.clientTop}const h=l&&!m&&!i?Bh(l,a,!0):Et(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+d.x+h.x,y:n.y*u.y-a.scrollTop*u.y+d.y+h.y}}function A1(e){return Array.from(e.getClientRects())}function T1(e){const t=Pt(e),n=Sl(e),r=e.ownerDocument.body,o=He(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=He(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+Iu(e);const s=-n.scrollTop;return gt(r).direction==="rtl"&&(l+=He(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:s}}function O1(e,t){const n=Ge(e),r=Pt(e),o=n.visualViewport;let i=r.clientWidth,l=r.clientHeight,s=0,a=0;if(o){i=o.width,l=o.height;const u=Lu();(!u||u&&t==="fixed")&&(s=o.offsetLeft,a=o.offsetTop)}return{width:i,height:l,x:s,y:a}}function L1(e,t){const n=Tn(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=bt(e)?ur(e):Et(1),l=e.clientWidth*i.x,s=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:l,height:s,x:a,y:u}}function Ed(e,t,n){let r;if(t==="viewport")r=O1(e,n);else if(t==="document")r=T1(Pt(e));else if(mt(t))r=L1(t,n);else{const o=Uh(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Yi(r)}function $h(e,t){const n=pn(e);return n===t||!mt(n)||wr(n)?!1:gt(n).position==="fixed"||$h(n,t)}function D1(e,t){const n=t.get(e);if(n)return n;let r=ko(e,[],!1).filter(s=>mt(s)&&Pr(s)!=="body"),o=null;const i=gt(e).position==="fixed";let l=i?pn(e):e;for(;mt(l)&&!wr(l);){const s=gt(l),a=Ou(l);!a&&s.position==="fixed"&&(o=null),(i?!a&&!o:!a&&s.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||To(l)&&!a&&$h(e,l))?r=r.filter(d=>d!==l):o=s,l=pn(l)}return t.set(e,r),r}function I1(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const l=[...n==="clippingAncestors"?wl(t)?[]:D1(t,this._c):[].concat(n),r],s=l[0],a=l.reduce((u,d)=>{const m=Ed(t,d,o);return u.top=He(m.top,u.top),u.right=dn(m.right,u.right),u.bottom=dn(m.bottom,u.bottom),u.left=He(m.left,u.left),u},Ed(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function z1(e){const{width:t,height:n}=jh(e);return{width:t,height:n}}function M1(e,t,n){const r=bt(t),o=Pt(t),i=n==="fixed",l=Tn(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const a=Et(0);if(r||!r&&!i)if((Pr(t)!=="body"||To(o))&&(s=Sl(t)),r){const h=Tn(t,!0,i,t);a.x=h.x+t.clientLeft,a.y=h.y+t.clientTop}else o&&(a.x=Iu(o));const u=o&&!r&&!i?Bh(o,s):Et(0),d=l.left+s.scrollLeft-a.x-u.x,m=l.top+s.scrollTop-a.y-u.y;return{x:d,y:m,width:l.width,height:l.height}}function fs(e){return gt(e).position==="static"}function kd(e,t){if(!bt(e)||gt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Pt(e)===n&&(n=n.ownerDocument.body),n}function Vh(e,t){const n=Ge(e);if(wl(e))return n;if(!bt(e)){let o=pn(e);for(;o&&!wr(o);){if(mt(o)&&!fs(o))return o;o=pn(o)}return n}let r=kd(e,t);for(;r&&k1(r)&&fs(r);)r=kd(r,t);return r&&wr(r)&&fs(r)&&!Ou(r)?n:r||b1(e)||n}const F1=async function(e){const t=this.getOffsetParent||Vh,n=this.getDimensions,r=await n(e.floating);return{reference:M1(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function j1(e){return gt(e).direction==="rtl"}const U1={convertOffsetParentRelativeRectToViewportRelativeRect:N1,getDocumentElement:Pt,getClippingRect:I1,getOffsetParent:Vh,getElementRects:F1,getClientRects:A1,getDimensions:z1,getScale:ur,isElement:mt,isRTL:j1};function B1(e,t){let n=null,r;const o=Pt(e);function i(){var s;clearTimeout(r),(s=n)==null||s.disconnect(),n=null}function l(s,a){s===void 0&&(s=!1),a===void 0&&(a=1),i();const{left:u,top:d,width:m,height:h}=e.getBoundingClientRect();if(s||t(),!m||!h)return;const y=oi(d),w=oi(o.clientWidth-(u+m)),p=oi(o.clientHeight-(d+h)),S=oi(u),c={rootMargin:-y+"px "+-w+"px "+-p+"px "+-S+"px",threshold:He(0,dn(1,a))||1};let v=!0;function x(C){const A=C[0].intersectionRatio;if(A!==a){if(!v)return l();A?l(!1,A):r=setTimeout(()=>{l(!1,1e-7)},1e3)}v=!1}try{n=new IntersectionObserver(x,{...c,root:o.ownerDocument})}catch{n=new IntersectionObserver(x,c)}n.observe(e)}return l(!0),i}function $1(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:l=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=Du(e),d=o||i?[...u?ko(u):[],...ko(t)]:[];d.forEach(f=>{o&&f.addEventListener("scroll",n,{passive:!0}),i&&f.addEventListener("resize",n)});const m=u&&s?B1(u,n):null;let h=-1,y=null;l&&(y=new ResizeObserver(f=>{let[c]=f;c&&c.target===u&&y&&(y.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var v;(v=y)==null||v.observe(t)})),n()}),u&&!a&&y.observe(u),y.observe(t));let w,p=a?Tn(e):null;a&&S();function S(){const f=Tn(e);p&&(f.x!==p.x||f.y!==p.y||f.width!==p.width||f.height!==p.height)&&n(),p=f,w=requestAnimationFrame(S)}return n(),()=>{var f;d.forEach(c=>{o&&c.removeEventListener("scroll",n),i&&c.removeEventListener("resize",n)}),m==null||m(),(f=y)==null||f.disconnect(),y=null,a&&cancelAnimationFrame(w)}}const V1=x1,H1=_1,W1=y1,K1=E1,G1=w1,bd=v1,Q1=C1,Y1=(e,t,n)=>{const r=new Map,o={platform:U1,...n},i={...o.platform,_c:r};return g1(e,t,{...o,platform:i})};var _i=typeof document<"u"?g.useLayoutEffect:g.useEffect;function Xi(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Xi(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Xi(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function Hh(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Pd(e,t){const n=Hh(e);return Math.round(t*n)/n}function ps(e){const t=g.useRef(e);return _i(()=>{t.current=e}),t}function X1(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:l}={},transform:s=!0,whileElementsMounted:a,open:u}=e,[d,m]=g.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,y]=g.useState(r);Xi(h,r)||y(r);const[w,p]=g.useState(null),[S,f]=g.useState(null),c=g.useCallback(b=>{b!==A.current&&(A.current=b,p(b))},[]),v=g.useCallback(b=>{b!==_.current&&(_.current=b,f(b))},[]),x=i||w,C=l||S,A=g.useRef(null),_=g.useRef(null),P=g.useRef(d),z=a!=null,E=ps(a),O=ps(o),R=ps(u),D=g.useCallback(()=>{if(!A.current||!_.current)return;const b={placement:t,strategy:n,middleware:h};O.current&&(b.platform=O.current),Y1(A.current,_.current,b).then(I=>{const F={...I,isPositioned:R.current!==!1};k.current&&!Xi(P.current,F)&&(P.current=F,Er.flushSync(()=>{m(F)}))})},[h,t,n,O,R]);_i(()=>{u===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,m(b=>({...b,isPositioned:!1})))},[u]);const k=g.useRef(!1);_i(()=>(k.current=!0,()=>{k.current=!1}),[]),_i(()=>{if(x&&(A.current=x),C&&(_.current=C),x&&C){if(E.current)return E.current(x,C,D);D()}},[x,C,D,E,z]);const U=g.useMemo(()=>({reference:A,floating:_,setReference:c,setFloating:v}),[c,v]),j=g.useMemo(()=>({reference:x,floating:C}),[x,C]),$=g.useMemo(()=>{const b={position:n,left:0,top:0};if(!j.floating)return b;const I=Pd(j.floating,d.x),F=Pd(j.floating,d.y);return s?{...b,transform:"translate("+I+"px, "+F+"px)",...Hh(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:I,top:F}},[n,s,j.floating,d.x,d.y]);return g.useMemo(()=>({...d,update:D,refs:U,elements:j,floatingStyles:$}),[d,D,U,j,$])}const Z1=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?bd({element:r.current,padding:o}).fn(n):{}:r?bd({element:r,padding:o}).fn(n):{}}}},J1=(e,t)=>({...V1(e),options:[e,t]}),q1=(e,t)=>({...H1(e),options:[e,t]}),eS=(e,t)=>({...Q1(e),options:[e,t]}),tS=(e,t)=>({...W1(e),options:[e,t]}),nS=(e,t)=>({...K1(e),options:[e,t]}),rS=(e,t)=>({...G1(e),options:[e,t]}),oS=(e,t)=>({...Z1(e),options:[e,t]});var iS="Arrow",Wh=g.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return N.jsx(de.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:N.jsx("polygon",{points:"0,0 30,0 15,10"})})});Wh.displayName=iS;var lS=Wh;function sS(e){const[t,n]=g.useState(void 0);return Te(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let l,s;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;l=u.inlineSize,s=u.blockSize}else l=e.offsetWidth,s=e.offsetHeight;n({width:l,height:s})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var zu="Popper",[Kh,xl]=ml(zu),[aS,Gh]=Kh(zu),Qh=e=>{const{__scopePopper:t,children:n}=e,[r,o]=g.useState(null);return N.jsx(aS,{scope:t,anchor:r,onAnchorChange:o,children:n})};Qh.displayName=zu;var Yh="PopperAnchor",Xh=g.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Gh(Yh,n),l=g.useRef(null),s=pe(t,l);return g.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||l.current)}),r?null:N.jsx(de.div,{...o,ref:s})});Xh.displayName=Yh;var Mu="PopperContent",[uS,cS]=Kh(Mu),Zh=g.forwardRef((e,t)=>{var V,J,G,W,H,X;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:l=0,arrowPadding:s=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:m="partial",hideWhenDetached:h=!1,updatePositionStrategy:y="optimized",onPlaced:w,...p}=e,S=Gh(Mu,n),[f,c]=g.useState(null),v=pe(t,De=>c(De)),[x,C]=g.useState(null),A=sS(x),_=(A==null?void 0:A.width)??0,P=(A==null?void 0:A.height)??0,z=r+(i!=="center"?"-"+i:""),E=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},O=Array.isArray(u)?u:[u],R=O.length>0,D={padding:E,boundary:O.filter(fS),altBoundary:R},{refs:k,floatingStyles:U,placement:j,isPositioned:$,middlewareData:b}=X1({strategy:"fixed",placement:z,whileElementsMounted:(...De)=>$1(...De,{animationFrame:y==="always"}),elements:{reference:S.anchor},middleware:[J1({mainAxis:o+P,alignmentAxis:l}),a&&q1({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?eS():void 0,...D}),a&&tS({...D}),nS({...D,apply:({elements:De,rects:Je,availableWidth:Nr,availableHeight:Ar})=>{const{width:Tr,height:tg}=Je.reference,Do=De.floating.style;Do.setProperty("--radix-popper-available-width",`${Nr}px`),Do.setProperty("--radix-popper-available-height",`${Ar}px`),Do.setProperty("--radix-popper-anchor-width",`${Tr}px`),Do.setProperty("--radix-popper-anchor-height",`${tg}px`)}}),x&&oS({element:x,padding:s}),pS({arrowWidth:_,arrowHeight:P}),h&&rS({strategy:"referenceHidden",...D})]}),[I,F]=em(j),B=jt(w);Te(()=>{$&&(B==null||B())},[$,B]);const q=(V=b.arrow)==null?void 0:V.x,Ee=(J=b.arrow)==null?void 0:J.y,ve=((G=b.arrow)==null?void 0:G.centerOffset)!==0,[Ze,he]=g.useState();return Te(()=>{f&&he(window.getComputedStyle(f).zIndex)},[f]),N.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:$?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ze,"--radix-popper-transform-origin":[(W=b.transformOrigin)==null?void 0:W.x,(H=b.transformOrigin)==null?void 0:H.y].join(" "),...((X=b.hide)==null?void 0:X.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:N.jsx(uS,{scope:n,placedSide:I,onArrowChange:C,arrowX:q,arrowY:Ee,shouldHideArrow:ve,children:N.jsx(de.div,{"data-side":I,"data-align":F,...p,ref:v,style:{...p.style,animation:$?void 0:"none"}})})})});Zh.displayName=Mu;var Jh="PopperArrow",dS={top:"bottom",right:"left",bottom:"top",left:"right"},qh=g.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=cS(Jh,r),l=dS[i.placedSide];return N.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:N.jsx(lS,{...o,ref:n,style:{...o.style,display:"block"}})})});qh.displayName=Jh;function fS(e){return e!==null}var pS=e=>({name:"transformOrigin",options:e,fn(t){var S,f,c;const{placement:n,rects:r,middlewareData:o}=t,l=((S=o.arrow)==null?void 0:S.centerOffset)!==0,s=l?0:e.arrowWidth,a=l?0:e.arrowHeight,[u,d]=em(n),m={start:"0%",center:"50%",end:"100%"}[d],h=(((f=o.arrow)==null?void 0:f.x)??0)+s/2,y=(((c=o.arrow)==null?void 0:c.y)??0)+a/2;let w="",p="";return u==="bottom"?(w=l?m:`${h}px`,p=`${-a}px`):u==="top"?(w=l?m:`${h}px`,p=`${r.floating.height+a}px`):u==="right"?(w=`${-a}px`,p=l?m:`${y}px`):u==="left"&&(w=`${r.floating.width+a}px`,p=l?m:`${y}px`),{data:{x:w,y:p}}}});function em(e){const[t,n="center"]=e.split("-");return[t,n]}var tm=Qh,nm=Xh,rm=Zh,om=qh,hS="VisuallyHidden",Fu=g.forwardRef((e,t)=>N.jsx(de.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Fu.displayName=hS;var mS=Fu,[_l,Dx]=ml("Tooltip",[xl]),Cl=xl(),im="TooltipProvider",gS=700,ya="tooltip.open",[vS,ju]=_l(im),lm=e=>{const{__scopeTooltip:t,delayDuration:n=gS,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[l,s]=g.useState(!0),a=g.useRef(!1),u=g.useRef(0);return g.useEffect(()=>{const d=u.current;return()=>window.clearTimeout(d)},[]),N.jsx(vS,{scope:t,isOpenDelayed:l,delayDuration:n,onOpen:g.useCallback(()=>{window.clearTimeout(u.current),s(!1)},[]),onClose:g.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>s(!0),r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:g.useCallback(d=>{a.current=d},[]),disableHoverableContent:o,children:i})};lm.displayName=im;var El="Tooltip",[yS,Oo]=_l(El),sm=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:i,disableHoverableContent:l,delayDuration:s}=e,a=ju(El,e.__scopeTooltip),u=Cl(t),[d,m]=g.useState(null),h=gl(),y=g.useRef(0),w=l??a.disableHoverableContent,p=s??a.delayDuration,S=g.useRef(!1),[f=!1,c]=fa({prop:r,defaultProp:o,onChange:_=>{_?(a.onOpen(),document.dispatchEvent(new CustomEvent(ya))):a.onClose(),i==null||i(_)}}),v=g.useMemo(()=>f?S.current?"delayed-open":"instant-open":"closed",[f]),x=g.useCallback(()=>{window.clearTimeout(y.current),y.current=0,S.current=!1,c(!0)},[c]),C=g.useCallback(()=>{window.clearTimeout(y.current),y.current=0,c(!1)},[c]),A=g.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{S.current=!0,c(!0),y.current=0},p)},[p,c]);return g.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),N.jsx(tm,{...u,children:N.jsx(yS,{scope:t,contentId:h,open:f,stateAttribute:v,trigger:d,onTriggerChange:m,onTriggerEnter:g.useCallback(()=>{a.isOpenDelayed?A():x()},[a.isOpenDelayed,A,x]),onTriggerLeave:g.useCallback(()=>{w?C():(window.clearTimeout(y.current),y.current=0)},[C,w]),onOpen:x,onClose:C,disableHoverableContent:w,children:n})})};sm.displayName=El;var wa="TooltipTrigger",am=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Oo(wa,n),i=ju(wa,n),l=Cl(n),s=g.useRef(null),a=pe(t,s,o.onTriggerChange),u=g.useRef(!1),d=g.useRef(!1),m=g.useCallback(()=>u.current=!1,[]);return g.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),N.jsx(nm,{asChild:!0,...l,children:N.jsx(de.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:a,onPointerMove:ee(e.onPointerMove,h=>{h.pointerType!=="touch"&&!d.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:ee(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:ee(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:ee(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:ee(e.onBlur,o.onClose),onClick:ee(e.onClick,o.onClose)})})});am.displayName=wa;var Uu="TooltipPortal",[wS,SS]=_l(Uu,{forceMount:void 0}),um=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=Oo(Uu,t);return N.jsx(wS,{scope:t,forceMount:n,children:N.jsx(Ru,{present:n||i.open,children:N.jsx(Pu,{asChild:!0,container:o,children:r})})})};um.displayName=Uu;var Sr="TooltipContent",cm=g.forwardRef((e,t)=>{const n=SS(Sr,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=Oo(Sr,e.__scopeTooltip);return N.jsx(Ru,{present:r||l.open,children:l.disableHoverableContent?N.jsx(dm,{side:o,...i,ref:t}):N.jsx(xS,{side:o,...i,ref:t})})}),xS=g.forwardRef((e,t)=>{const n=Oo(Sr,e.__scopeTooltip),r=ju(Sr,e.__scopeTooltip),o=g.useRef(null),i=pe(t,o),[l,s]=g.useState(null),{trigger:a,onClose:u}=n,d=o.current,{onPointerInTransitChange:m}=r,h=g.useCallback(()=>{s(null),m(!1)},[m]),y=g.useCallback((w,p)=>{const S=w.currentTarget,f={x:w.clientX,y:w.clientY},c=kS(f,S.getBoundingClientRect()),v=bS(f,c),x=PS(p.getBoundingClientRect()),C=NS([...v,...x]);s(C),m(!0)},[m]);return g.useEffect(()=>()=>h(),[h]),g.useEffect(()=>{if(a&&d){const w=S=>y(S,d),p=S=>y(S,a);return a.addEventListener("pointerleave",w),d.addEventListener("pointerleave",p),()=>{a.removeEventListener("pointerleave",w),d.removeEventListener("pointerleave",p)}}},[a,d,y,h]),g.useEffect(()=>{if(l){const w=p=>{const S=p.target,f={x:p.clientX,y:p.clientY},c=(a==null?void 0:a.contains(S))||(d==null?void 0:d.contains(S)),v=!RS(f,l);c?h():v&&(h(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[a,d,l,u,h]),N.jsx(dm,{...e,ref:i})}),[_S,CS]=_l(El,{isInside:!1}),dm=g.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:l,...s}=e,a=Oo(Sr,n),u=Cl(n),{onClose:d}=a;return g.useEffect(()=>(document.addEventListener(ya,d),()=>document.removeEventListener(ya,d)),[d]),g.useEffect(()=>{if(a.trigger){const m=h=>{const y=h.target;y!=null&&y.contains(a.trigger)&&d()};return window.addEventListener("scroll",m,{capture:!0}),()=>window.removeEventListener("scroll",m,{capture:!0})}},[a.trigger,d]),N.jsx(bu,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:m=>m.preventDefault(),onDismiss:d,children:N.jsxs(rm,{"data-state":a.stateAttribute,...u,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[N.jsx(ph,{children:r}),N.jsx(_S,{scope:n,isInside:!0,children:N.jsx(mS,{id:a.contentId,role:"tooltip",children:o||r})})]})})});cm.displayName=Sr;var fm="TooltipArrow",ES=g.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Cl(n);return CS(fm,n).isInside?null:N.jsx(om,{...o,...r,ref:t})});ES.displayName=fm;function kS(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function bS(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function PS(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function RS(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,l=t.length-1;i<t.length;l=i++){const s=t[i].x,a=t[i].y,u=t[l].x,d=t[l].y;a>r!=d>r&&n<(u-s)*(r-a)/(d-a)+s&&(o=!o)}return o}function NS(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),AS(t)}function AS(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],l=t[t.length-2];if((i.x-l.x)*(o.y-l.y)>=(i.y-l.y)*(o.x-l.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],l=n[n.length-2];if((i.x-l.x)*(o.y-l.y)>=(i.y-l.y)*(o.x-l.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var TS=lm,OS=sm,LS=am,DS=um,pm=cm;const Ix=TS,zx=({delayDuration:e=500,...t})=>N.jsx(OS,{delayDuration:e,...t}),Mx=LS,IS=g.forwardRef(({className:e,sideOffset:t=4,...n},r)=>N.jsx(DS,{children:N.jsx(pm,{ref:r,sideOffset:t,className:kt("z-50 overflow-hidden rounded-md bg-zinc-900 px-3 py-1.5 text-xs text-zinc-100 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));IS.displayName=pm.displayName;var zS=g.createContext(void 0);function MS(e){const t=g.useContext(zS);return e||t||"ltr"}function FS(e){const t=g.useRef({value:e,previous:e});return g.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function Rd(e,[t,n]){return Math.min(n,Math.max(t,e))}function jS(e){const t=e+"CollectionProvider",[n,r]=ml(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),l=y=>{const{scope:w,children:p}=y,S=Nt.useRef(null),f=Nt.useRef(new Map).current;return N.jsx(o,{scope:w,itemMap:f,collectionRef:S,children:p})};l.displayName=t;const s=e+"CollectionSlot",a=Nt.forwardRef((y,w)=>{const{scope:p,children:S}=y,f=i(s,p),c=pe(w,f.collectionRef);return N.jsx(yr,{ref:c,children:S})});a.displayName=s;const u=e+"CollectionItemSlot",d="data-radix-collection-item",m=Nt.forwardRef((y,w)=>{const{scope:p,children:S,...f}=y,c=Nt.useRef(null),v=pe(w,c),x=i(u,p);return Nt.useEffect(()=>(x.itemMap.set(c,{ref:c,...f}),()=>void x.itemMap.delete(c))),N.jsx(yr,{[d]:"",ref:v,children:S})});m.displayName=u;function h(y){const w=i(e+"CollectionConsumer",y);return Nt.useCallback(()=>{const S=w.collectionRef.current;if(!S)return[];const f=Array.from(S.querySelectorAll(`[${d}]`));return Array.from(w.itemMap.values()).sort((x,C)=>f.indexOf(x.ref.current)-f.indexOf(C.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:l,Slot:a,ItemSlot:m},h,r]}var US=[" ","Enter","ArrowUp","ArrowDown"],BS=[" ","Enter"],Lo="Select",[kl,bl,$S]=jS(Lo),[Rr,Fx]=ml(Lo,[$S,xl]),Pl=xl(),[VS,vn]=Rr(Lo),[HS,WS]=Rr(Lo),hm=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:l,defaultValue:s,onValueChange:a,dir:u,name:d,autoComplete:m,disabled:h,required:y,form:w}=e,p=Pl(t),[S,f]=g.useState(null),[c,v]=g.useState(null),[x,C]=g.useState(!1),A=MS(u),[_=!1,P]=fa({prop:r,defaultProp:o,onChange:i}),[z,E]=fa({prop:l,defaultProp:s,onChange:a}),O=g.useRef(null),R=S?w||!!S.closest("form"):!0,[D,k]=g.useState(new Set),U=Array.from(D).map(j=>j.props.value).join(";");return N.jsx(tm,{...p,children:N.jsxs(VS,{required:y,scope:t,trigger:S,onTriggerChange:f,valueNode:c,onValueNodeChange:v,valueNodeHasChildren:x,onValueNodeHasChildrenChange:C,contentId:gl(),value:z,onValueChange:E,open:_,onOpenChange:P,dir:A,triggerPointerDownPosRef:O,disabled:h,children:[N.jsx(kl.Provider,{scope:t,children:N.jsx(HS,{scope:e.__scopeSelect,onNativeOptionAdd:g.useCallback(j=>{k($=>new Set($).add(j))},[]),onNativeOptionRemove:g.useCallback(j=>{k($=>{const b=new Set($);return b.delete(j),b})},[]),children:n})}),R?N.jsxs(jm,{"aria-hidden":!0,required:y,tabIndex:-1,name:d,autoComplete:m,value:z,onChange:j=>E(j.target.value),disabled:h,form:w,children:[z===void 0?N.jsx("option",{value:""}):null,Array.from(D)]},U):null]})})};hm.displayName=Lo;var mm="SelectTrigger",gm=g.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,i=Pl(n),l=vn(mm,n),s=l.disabled||r,a=pe(t,l.onTriggerChange),u=bl(n),d=g.useRef("touch"),[m,h,y]=Um(p=>{const S=u().filter(v=>!v.disabled),f=S.find(v=>v.value===l.value),c=Bm(S,p,f);c!==void 0&&l.onValueChange(c.value)}),w=p=>{s||(l.onOpenChange(!0),y()),p&&(l.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return N.jsx(nm,{asChild:!0,...i,children:N.jsx(de.button,{type:"button",role:"combobox","aria-controls":l.contentId,"aria-expanded":l.open,"aria-required":l.required,"aria-autocomplete":"none",dir:l.dir,"data-state":l.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":Fm(l.value)?"":void 0,...o,ref:a,onClick:ee(o.onClick,p=>{p.currentTarget.focus(),d.current!=="mouse"&&w(p)}),onPointerDown:ee(o.onPointerDown,p=>{d.current=p.pointerType;const S=p.target;S.hasPointerCapture(p.pointerId)&&S.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(w(p),p.preventDefault())}),onKeyDown:ee(o.onKeyDown,p=>{const S=m.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&h(p.key),!(S&&p.key===" ")&&US.includes(p.key)&&(w(),p.preventDefault())})})})});gm.displayName=mm;var vm="SelectValue",ym=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...s}=e,a=vn(vm,n),{onValueNodeHasChildrenChange:u}=a,d=i!==void 0,m=pe(t,a.onValueNodeChange);return Te(()=>{u(d)},[u,d]),N.jsx(de.span,{...s,ref:m,style:{pointerEvents:"none"},children:Fm(a.value)?N.jsx(N.Fragment,{children:l}):i})});ym.displayName=vm;var KS="SelectIcon",wm=g.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return N.jsx(de.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});wm.displayName=KS;var GS="SelectPortal",Sm=e=>N.jsx(Pu,{asChild:!0,...e});Sm.displayName=GS;var On="SelectContent",xm=g.forwardRef((e,t)=>{const n=vn(On,e.__scopeSelect),[r,o]=g.useState();if(Te(()=>{o(new DocumentFragment)},[]),!n.open){const i=r;return i?Er.createPortal(N.jsx(_m,{scope:e.__scopeSelect,children:N.jsx(kl.Slot,{scope:e.__scopeSelect,children:N.jsx("div",{children:e.children})})}),i):null}return N.jsx(Cm,{...e,ref:t})});xm.displayName=On;var at=10,[_m,yn]=Rr(On),QS="SelectContentImpl",Cm=g.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:l,side:s,sideOffset:a,align:u,alignOffset:d,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:w,hideWhenDetached:p,avoidCollisions:S,...f}=e,c=vn(On,n),[v,x]=g.useState(null),[C,A]=g.useState(null),_=pe(t,V=>x(V)),[P,z]=g.useState(null),[E,O]=g.useState(null),R=bl(n),[D,k]=g.useState(!1),U=g.useRef(!1);g.useEffect(()=>{if(v)return s1(v)},[v]),x0();const j=g.useCallback(V=>{const[J,...G]=R().map(X=>X.ref.current),[W]=G.slice(-1),H=document.activeElement;for(const X of V)if(X===H||(X==null||X.scrollIntoView({block:"nearest"}),X===J&&C&&(C.scrollTop=0),X===W&&C&&(C.scrollTop=C.scrollHeight),X==null||X.focus(),document.activeElement!==H))return},[R,C]),$=g.useCallback(()=>j([P,v]),[j,P,v]);g.useEffect(()=>{D&&$()},[D,$]);const{onOpenChange:b,triggerPointerDownPosRef:I}=c;g.useEffect(()=>{if(v){let V={x:0,y:0};const J=W=>{var H,X;V={x:Math.abs(Math.round(W.pageX)-(((H=I.current)==null?void 0:H.x)??0)),y:Math.abs(Math.round(W.pageY)-(((X=I.current)==null?void 0:X.y)??0))}},G=W=>{V.x<=10&&V.y<=10?W.preventDefault():v.contains(W.target)||b(!1),document.removeEventListener("pointermove",J),I.current=null};return I.current!==null&&(document.addEventListener("pointermove",J),document.addEventListener("pointerup",G,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",J),document.removeEventListener("pointerup",G,{capture:!0})}}},[v,b,I]),g.useEffect(()=>{const V=()=>b(!1);return window.addEventListener("blur",V),window.addEventListener("resize",V),()=>{window.removeEventListener("blur",V),window.removeEventListener("resize",V)}},[b]);const[F,B]=Um(V=>{const J=R().filter(H=>!H.disabled),G=J.find(H=>H.ref.current===document.activeElement),W=Bm(J,V,G);W&&setTimeout(()=>W.ref.current.focus())}),q=g.useCallback((V,J,G)=>{const W=!U.current&&!G;(c.value!==void 0&&c.value===J||W)&&(z(V),W&&(U.current=!0))},[c.value]),Ee=g.useCallback(()=>v==null?void 0:v.focus(),[v]),ve=g.useCallback((V,J,G)=>{const W=!U.current&&!G;(c.value!==void 0&&c.value===J||W)&&O(V)},[c.value]),Ze=r==="popper"?Sa:Em,he=Ze===Sa?{side:s,sideOffset:a,align:u,alignOffset:d,arrowPadding:m,collisionBoundary:h,collisionPadding:y,sticky:w,hideWhenDetached:p,avoidCollisions:S}:{};return N.jsx(_m,{scope:n,content:v,viewport:C,onViewportChange:A,itemRefCallback:q,selectedItem:P,onItemLeave:Ee,itemTextRefCallback:ve,focusSelectedItem:$,selectedItemText:E,position:r,isPositioned:D,searchRef:F,children:N.jsx(Dh,{as:yr,allowPinchZoom:!0,children:N.jsx(kh,{asChild:!0,trapped:c.open,onMountAutoFocus:V=>{V.preventDefault()},onUnmountAutoFocus:ee(o,V=>{var J;(J=c.trigger)==null||J.focus({preventScroll:!0}),V.preventDefault()}),children:N.jsx(bu,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:V=>V.preventDefault(),onDismiss:()=>c.onOpenChange(!1),children:N.jsx(Ze,{role:"listbox",id:c.contentId,"data-state":c.open?"open":"closed",dir:c.dir,onContextMenu:V=>V.preventDefault(),...f,...he,onPlaced:()=>k(!0),ref:_,style:{display:"flex",flexDirection:"column",outline:"none",...f.style},onKeyDown:ee(f.onKeyDown,V=>{const J=V.ctrlKey||V.altKey||V.metaKey;if(V.key==="Tab"&&V.preventDefault(),!J&&V.key.length===1&&B(V.key),["ArrowUp","ArrowDown","Home","End"].includes(V.key)){let W=R().filter(H=>!H.disabled).map(H=>H.ref.current);if(["ArrowUp","End"].includes(V.key)&&(W=W.slice().reverse()),["ArrowUp","ArrowDown"].includes(V.key)){const H=V.target,X=W.indexOf(H);W=W.slice(X+1)}setTimeout(()=>j(W)),V.preventDefault()}})})})})})})});Cm.displayName=QS;var YS="SelectItemAlignedPosition",Em=g.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,i=vn(On,n),l=yn(On,n),[s,a]=g.useState(null),[u,d]=g.useState(null),m=pe(t,_=>d(_)),h=bl(n),y=g.useRef(!1),w=g.useRef(!0),{viewport:p,selectedItem:S,selectedItemText:f,focusSelectedItem:c}=l,v=g.useCallback(()=>{if(i.trigger&&i.valueNode&&s&&u&&p&&S&&f){const _=i.trigger.getBoundingClientRect(),P=u.getBoundingClientRect(),z=i.valueNode.getBoundingClientRect(),E=f.getBoundingClientRect();if(i.dir!=="rtl"){const H=E.left-P.left,X=z.left-H,De=_.left-X,Je=_.width+De,Nr=Math.max(Je,P.width),Ar=window.innerWidth-at,Tr=Rd(X,[at,Math.max(at,Ar-Nr)]);s.style.minWidth=Je+"px",s.style.left=Tr+"px"}else{const H=P.right-E.right,X=window.innerWidth-z.right-H,De=window.innerWidth-_.right-X,Je=_.width+De,Nr=Math.max(Je,P.width),Ar=window.innerWidth-at,Tr=Rd(X,[at,Math.max(at,Ar-Nr)]);s.style.minWidth=Je+"px",s.style.right=Tr+"px"}const O=h(),R=window.innerHeight-at*2,D=p.scrollHeight,k=window.getComputedStyle(u),U=parseInt(k.borderTopWidth,10),j=parseInt(k.paddingTop,10),$=parseInt(k.borderBottomWidth,10),b=parseInt(k.paddingBottom,10),I=U+j+D+b+$,F=Math.min(S.offsetHeight*5,I),B=window.getComputedStyle(p),q=parseInt(B.paddingTop,10),Ee=parseInt(B.paddingBottom,10),ve=_.top+_.height/2-at,Ze=R-ve,he=S.offsetHeight/2,V=S.offsetTop+he,J=U+j+V,G=I-J;if(J<=ve){const H=O.length>0&&S===O[O.length-1].ref.current;s.style.bottom="0px";const X=u.clientHeight-p.offsetTop-p.offsetHeight,De=Math.max(Ze,he+(H?Ee:0)+X+$),Je=J+De;s.style.height=Je+"px"}else{const H=O.length>0&&S===O[0].ref.current;s.style.top="0px";const De=Math.max(ve,U+p.offsetTop+(H?q:0)+he)+G;s.style.height=De+"px",p.scrollTop=J-ve+p.offsetTop}s.style.margin=`${at}px 0`,s.style.minHeight=F+"px",s.style.maxHeight=R+"px",r==null||r(),requestAnimationFrame(()=>y.current=!0)}},[h,i.trigger,i.valueNode,s,u,p,S,f,i.dir,r]);Te(()=>v(),[v]);const[x,C]=g.useState();Te(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);const A=g.useCallback(_=>{_&&w.current===!0&&(v(),c==null||c(),w.current=!1)},[v,c]);return N.jsx(ZS,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:A,children:N.jsx("div",{ref:a,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:x},children:N.jsx(de.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});Em.displayName=YS;var XS="SelectPopperPosition",Sa=g.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=at,...i}=e,l=Pl(n);return N.jsx(rm,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Sa.displayName=XS;var[ZS,Bu]=Rr(On,{}),xa="SelectViewport",km=g.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,i=yn(xa,n),l=Bu(xa,n),s=pe(t,i.onViewportChange),a=g.useRef(0);return N.jsxs(N.Fragment,{children:[N.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),N.jsx(kl.Slot,{scope:n,children:N.jsx(de.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:ee(o.onScroll,u=>{const d=u.currentTarget,{contentWrapper:m,shouldExpandOnScrollRef:h}=l;if(h!=null&&h.current&&m){const y=Math.abs(a.current-d.scrollTop);if(y>0){const w=window.innerHeight-at*2,p=parseFloat(m.style.minHeight),S=parseFloat(m.style.height),f=Math.max(p,S);if(f<w){const c=f+y,v=Math.min(w,c),x=c-v;m.style.height=v+"px",m.style.bottom==="0px"&&(d.scrollTop=x>0?x:0,m.style.justifyContent="flex-end")}}}a.current=d.scrollTop})})})]})});km.displayName=xa;var bm="SelectGroup",[JS,qS]=Rr(bm),ex=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=gl();return N.jsx(JS,{scope:n,id:o,children:N.jsx(de.div,{role:"group","aria-labelledby":o,...r,ref:t})})});ex.displayName=bm;var Pm="SelectLabel",Rm=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=qS(Pm,n);return N.jsx(de.div,{id:o.id,...r,ref:t})});Rm.displayName=Pm;var Zi="SelectItem",[tx,Nm]=Rr(Zi),Am=g.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:i,...l}=e,s=vn(Zi,n),a=yn(Zi,n),u=s.value===r,[d,m]=g.useState(i??""),[h,y]=g.useState(!1),w=pe(t,c=>{var v;return(v=a.itemRefCallback)==null?void 0:v.call(a,c,r,o)}),p=gl(),S=g.useRef("touch"),f=()=>{o||(s.onValueChange(r),s.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return N.jsx(tx,{scope:n,value:r,disabled:o,textId:p,isSelected:u,onItemTextChange:g.useCallback(c=>{m(v=>v||((c==null?void 0:c.textContent)??"").trim())},[]),children:N.jsx(kl.ItemSlot,{scope:n,value:r,disabled:o,textValue:d,children:N.jsx(de.div,{role:"option","aria-labelledby":p,"data-highlighted":h?"":void 0,"aria-selected":u&&h,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...l,ref:w,onFocus:ee(l.onFocus,()=>y(!0)),onBlur:ee(l.onBlur,()=>y(!1)),onClick:ee(l.onClick,()=>{S.current!=="mouse"&&f()}),onPointerUp:ee(l.onPointerUp,()=>{S.current==="mouse"&&f()}),onPointerDown:ee(l.onPointerDown,c=>{S.current=c.pointerType}),onPointerMove:ee(l.onPointerMove,c=>{var v;S.current=c.pointerType,o?(v=a.onItemLeave)==null||v.call(a):S.current==="mouse"&&c.currentTarget.focus({preventScroll:!0})}),onPointerLeave:ee(l.onPointerLeave,c=>{var v;c.currentTarget===document.activeElement&&((v=a.onItemLeave)==null||v.call(a))}),onKeyDown:ee(l.onKeyDown,c=>{var x;((x=a.searchRef)==null?void 0:x.current)!==""&&c.key===" "||(BS.includes(c.key)&&f(),c.key===" "&&c.preventDefault())})})})})});Am.displayName=Zi;var Gr="SelectItemText",Tm=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...i}=e,l=vn(Gr,n),s=yn(Gr,n),a=Nm(Gr,n),u=WS(Gr,n),[d,m]=g.useState(null),h=pe(t,f=>m(f),a.onItemTextChange,f=>{var c;return(c=s.itemTextRefCallback)==null?void 0:c.call(s,f,a.value,a.disabled)}),y=d==null?void 0:d.textContent,w=g.useMemo(()=>N.jsx("option",{value:a.value,disabled:a.disabled,children:y},a.value),[a.disabled,a.value,y]),{onNativeOptionAdd:p,onNativeOptionRemove:S}=u;return Te(()=>(p(w),()=>S(w)),[p,S,w]),N.jsxs(N.Fragment,{children:[N.jsx(de.span,{id:a.textId,...i,ref:h}),a.isSelected&&l.valueNode&&!l.valueNodeHasChildren?Er.createPortal(i.children,l.valueNode):null]})});Tm.displayName=Gr;var Om="SelectItemIndicator",Lm=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return Nm(Om,n).isSelected?N.jsx(de.span,{"aria-hidden":!0,...r,ref:t}):null});Lm.displayName=Om;var _a="SelectScrollUpButton",Dm=g.forwardRef((e,t)=>{const n=yn(_a,e.__scopeSelect),r=Bu(_a,e.__scopeSelect),[o,i]=g.useState(!1),l=pe(t,r.onScrollButtonChange);return Te(()=>{if(n.viewport&&n.isPositioned){let s=function(){const u=a.scrollTop>0;i(u)};const a=n.viewport;return s(),a.addEventListener("scroll",s),()=>a.removeEventListener("scroll",s)}},[n.viewport,n.isPositioned]),o?N.jsx(zm,{...e,ref:l,onAutoScroll:()=>{const{viewport:s,selectedItem:a}=n;s&&a&&(s.scrollTop=s.scrollTop-a.offsetHeight)}}):null});Dm.displayName=_a;var Ca="SelectScrollDownButton",Im=g.forwardRef((e,t)=>{const n=yn(Ca,e.__scopeSelect),r=Bu(Ca,e.__scopeSelect),[o,i]=g.useState(!1),l=pe(t,r.onScrollButtonChange);return Te(()=>{if(n.viewport&&n.isPositioned){let s=function(){const u=a.scrollHeight-a.clientHeight,d=Math.ceil(a.scrollTop)<u;i(d)};const a=n.viewport;return s(),a.addEventListener("scroll",s),()=>a.removeEventListener("scroll",s)}},[n.viewport,n.isPositioned]),o?N.jsx(zm,{...e,ref:l,onAutoScroll:()=>{const{viewport:s,selectedItem:a}=n;s&&a&&(s.scrollTop=s.scrollTop+a.offsetHeight)}}):null});Im.displayName=Ca;var zm=g.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,i=yn("SelectScrollButton",n),l=g.useRef(null),s=bl(n),a=g.useCallback(()=>{l.current!==null&&(window.clearInterval(l.current),l.current=null)},[]);return g.useEffect(()=>()=>a(),[a]),Te(()=>{var d;const u=s().find(m=>m.ref.current===document.activeElement);(d=u==null?void 0:u.ref.current)==null||d.scrollIntoView({block:"nearest"})},[s]),N.jsx(de.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:ee(o.onPointerDown,()=>{l.current===null&&(l.current=window.setInterval(r,50))}),onPointerMove:ee(o.onPointerMove,()=>{var u;(u=i.onItemLeave)==null||u.call(i),l.current===null&&(l.current=window.setInterval(r,50))}),onPointerLeave:ee(o.onPointerLeave,()=>{a()})})}),nx="SelectSeparator",Mm=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return N.jsx(de.div,{"aria-hidden":!0,...r,ref:t})});Mm.displayName=nx;var Ea="SelectArrow",rx=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Pl(n),i=vn(Ea,n),l=yn(Ea,n);return i.open&&l.position==="popper"?N.jsx(om,{...o,...r,ref:t}):null});rx.displayName=Ea;function Fm(e){return e===""||e===void 0}var jm=g.forwardRef((e,t)=>{const{value:n,...r}=e,o=g.useRef(null),i=pe(t,o),l=FS(n);return g.useEffect(()=>{const s=o.current,a=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(a,"value").set;if(l!==n&&d){const m=new Event("change",{bubbles:!0});d.call(s,n),s.dispatchEvent(m)}},[l,n]),N.jsx(Fu,{asChild:!0,children:N.jsx("select",{...r,ref:i,defaultValue:n})})});jm.displayName="BubbleSelect";function Um(e){const t=jt(e),n=g.useRef(""),r=g.useRef(0),o=g.useCallback(l=>{const s=n.current+l;t(s),function a(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>a(""),1e3))}(s)},[t]),i=g.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return g.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function Bm(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let l=ox(e,Math.max(i,0));o.length===1&&(l=l.filter(u=>u!==n));const a=l.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}function ox(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var ix=hm,$m=gm,lx=ym,sx=wm,ax=Sm,Vm=xm,ux=km,Hm=Rm,Wm=Am,cx=Tm,dx=Lm,Km=Dm,Gm=Im,Qm=Mm;const jx=ix,Ux=lx,fx=g.forwardRef(({className:e,children:t,...n},r)=>N.jsxs($m,{ref:r,className:kt("flex w-full items-center justify-between whitespace-nowrap rounded-md bg-transparent p-0 text-xs shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,N.jsx(sx,{asChild:!0,children:N.jsx(vh,{className:"h-3 w-3 ml-0.5 mt-0.5 opacity-50"})})]}));fx.displayName=$m.displayName;const Ym=g.forwardRef(({className:e,...t},n)=>N.jsx(Km,{ref:n,className:kt("flex cursor-default items-center justify-center py-1",e),...t,children:N.jsx(mw,{className:"h-4 w-4"})}));Ym.displayName=Km.displayName;const Xm=g.forwardRef(({className:e,...t},n)=>N.jsx(Gm,{ref:n,className:kt("flex cursor-default items-center justify-center py-1",e),...t,children:N.jsx(vh,{className:"h-4 w-4"})}));Xm.displayName=Gm.displayName;const px=g.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>N.jsx(ax,{children:N.jsxs(Vm,{ref:o,className:kt("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[N.jsx(Ym,{}),N.jsx(ux,{className:kt("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),N.jsx(Xm,{})]})}));px.displayName=Vm.displayName;const hx=g.forwardRef(({className:e,...t},n)=>N.jsx(Hm,{ref:n,className:kt("px-2 py-1.5 text-sm font-semibold",e),...t}));hx.displayName=Hm.displayName;const mx=g.forwardRef(({className:e,children:t,...n},r)=>N.jsxs(Wm,{ref:r,className:kt("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[N.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:N.jsx(dx,{children:N.jsx(hw,{className:"h-4 w-4"})})}),N.jsx(cx,{children:t})]}));mx.displayName=Wm.displayName;const gx=g.forwardRef(({className:e,...t},n)=>N.jsx(Qm,{ref:n,className:kt("-mx-1 my-1 h-px bg-muted",e),...t}));gx.displayName=Qm.displayName;var Nd;(function(e){e[e.Audio=1]="Audio",e[e.Cache=2]="Cache",e[e.Config=3]="Config",e[e.Data=4]="Data",e[e.LocalData=5]="LocalData",e[e.Document=6]="Document",e[e.Download=7]="Download",e[e.Picture=8]="Picture",e[e.Public=9]="Public",e[e.Video=10]="Video",e[e.Resource=11]="Resource",e[e.Temp=12]="Temp",e[e.AppConfig=13]="AppConfig",e[e.AppData=14]="AppData",e[e.AppLocalData=15]="AppLocalData",e[e.AppCache=16]="AppCache",e[e.AppLog=17]="AppLog",e[e.Desktop=18]="Desktop",e[e.Executable=19]="Executable",e[e.Font=20]="Font",e[e.Home=21]="Home",e[e.Runtime=22]="Runtime",e[e.Template=23]="Template"})(Nd||(Nd={}));var Ad;(function(e){e[e.Start=0]="Start",e[e.Current=1]="Current",e[e.End=2]="End"})(Ad||(Ad={}));function vx(e){return{isFile:e.isFile,isDirectory:e.isDirectory,isSymlink:e.isSymlink,size:e.size,mtime:e.mtime!==null?new Date(e.mtime):null,atime:e.atime!==null?new Date(e.atime):null,birthtime:e.birthtime!==null?new Date(e.birthtime):null,readonly:e.readonly,fileAttributes:e.fileAttributes,dev:e.dev,ino:e.ino,mode:e.mode,nlink:e.nlink,uid:e.uid,gid:e.gid,rdev:e.rdev,blksize:e.blksize,blocks:e.blocks}}function yx(e){const t=new Uint8ClampedArray(e),n=t.byteLength;let r=0;for(let o=0;o<n;o++){const i=t[o];r*=256,r+=i}return r}class wx extends Xp{async read(t){if(t.byteLength===0)return 0;const n=await T("plugin:fs|read",{rid:this.rid,len:t.byteLength}),r=yx(n.slice(-8)),o=n instanceof ArrayBuffer?new Uint8Array(n):n;return t.set(o.slice(0,o.length-8)),r===0?null:r}async seek(t,n){return await T("plugin:fs|seek",{rid:this.rid,offset:t,whence:n})}async stat(){const t=await T("plugin:fs|fstat",{rid:this.rid});return vx(t)}async truncate(t){await T("plugin:fs|ftruncate",{rid:this.rid,len:t})}async write(t){return await T("plugin:fs|write",{rid:this.rid,data:t})}}async function Sx(e,t){if(e instanceof URL&&e.protocol!=="file:")throw new TypeError("Must be a file URL.");const n=await T("plugin:fs|open",{path:e instanceof URL?e.toString():e,options:t});return new wx(n)}async function Bx(e,t){if(e instanceof URL&&e.protocol!=="file:")throw new TypeError("Must be a file URL.");const n=await T("plugin:fs|read_file",{path:e instanceof URL?e.toString():e,options:t});return n instanceof ArrayBuffer?new Uint8Array(n):Uint8Array.from(n)}async function $x(e,t,n){if(e instanceof URL&&e.protocol!=="file:")throw new TypeError("Must be a file URL.");if(t instanceof ReadableStream){const r=await Sx(e,n);for await(const o of t)await r.write(o);await r.close()}else await T("plugin:fs|write_file",t,{headers:{path:encodeURIComponent(e instanceof URL?e.toString():e),options:JSON.stringify(n)}})}class Zm{constructor(...t){this.type="Logical",t.length===1?"Logical"in t[0]?(this.width=t[0].Logical.width,this.height=t[0].Logical.height):(this.width=t[0].width,this.height=t[0].height):(this.width=t[0],this.height=t[1])}toPhysical(t){return new cr(this.width*t,this.height*t)}[Be](){return{width:this.width,height:this.height}}toJSON(){return this[Be]()}}class cr{constructor(...t){this.type="Physical",t.length===1?"Physical"in t[0]?(this.width=t[0].Physical.width,this.height=t[0].Physical.height):(this.width=t[0].width,this.height=t[0].height):(this.width=t[0],this.height=t[1])}toLogical(t){return new Zm(this.width/t,this.height/t)}[Be](){return{width:this.width,height:this.height}}toJSON(){return this[Be]()}}class Yt{constructor(t){this.size=t}toLogical(t){return this.size instanceof Zm?this.size:this.size.toLogical(t)}toPhysical(t){return this.size instanceof cr?this.size:this.size.toPhysical(t)}[Be](){return{[`${this.size.type}`]:{width:this.size.width,height:this.size.height}}}toJSON(){return this[Be]()}}class Jm{constructor(...t){this.type="Logical",t.length===1?"Logical"in t[0]?(this.x=t[0].Logical.x,this.y=t[0].Logical.y):(this.x=t[0].x,this.y=t[0].y):(this.x=t[0],this.y=t[1])}toPhysical(t){return new tt(this.x*t,this.y*t)}[Be](){return{x:this.x,y:this.y}}toJSON(){return this[Be]()}}class tt{constructor(...t){this.type="Physical",t.length===1?"Physical"in t[0]?(this.x=t[0].Physical.x,this.y=t[0].Physical.y):(this.x=t[0].x,this.y=t[0].y):(this.x=t[0],this.y=t[1])}toLogical(t){return new Jm(this.x/t,this.y/t)}[Be](){return{x:this.x,y:this.y}}toJSON(){return this[Be]()}}class er{constructor(t){this.position=t}toLogical(t){return this.position instanceof Jm?this.position:this.position.toLogical(t)}toPhysical(t){return this.position instanceof tt?this.position:this.position.toPhysical(t)}[Be](){return{[`${this.position.type}`]:{x:this.position.x,y:this.position.y}}}toJSON(){return this[Be]()}}class io extends Xp{constructor(t){super(t)}static async new(t,n,r){return T("plugin:image|new",{rgba:Ji(t),width:n,height:r}).then(o=>new io(o))}static async fromBytes(t){return T("plugin:image|from_bytes",{bytes:Ji(t)}).then(n=>new io(n))}static async fromPath(t){return T("plugin:image|from_path",{path:t}).then(n=>new io(n))}async rgba(){return T("plugin:image|rgba",{rid:this.rid}).then(t=>new Uint8Array(t))}async size(){return T("plugin:image|size",{rid:this.rid})}}function Ji(e){return e==null?null:typeof e=="string"?e:e instanceof io?e.rid:e}var ka;(function(e){e[e.Critical=1]="Critical",e[e.Informational=2]="Informational"})(ka||(ka={}));class xx{constructor(t){this._preventDefault=!1,this.event=t.event,this.id=t.id}preventDefault(){this._preventDefault=!0}isPreventDefault(){return this._preventDefault}}var Td;(function(e){e.None="none",e.Normal="normal",e.Indeterminate="indeterminate",e.Paused="paused",e.Error="error"})(Td||(Td={}));function qm(){return new $u(window.__TAURI_INTERNALS__.metadata.currentWindow.label,{skip:!0})}async function hs(){return T("plugin:window|get_all_windows").then(e=>e.map(t=>new $u(t,{skip:!0})))}const ms=["tauri://created","tauri://error"];class $u{constructor(t,n={}){var r;this.label=t,this.listeners=Object.create(null),n!=null&&n.skip||T("plugin:window|create",{options:{...n,parent:typeof n.parent=="string"?n.parent:(r=n.parent)===null||r===void 0?void 0:r.label,label:t}}).then(async()=>this.emit("tauri://created")).catch(async o=>this.emit("tauri://error",o))}static async getByLabel(t){var n;return(n=(await hs()).find(r=>r.label===t))!==null&&n!==void 0?n:null}static getCurrent(){return qm()}static async getAll(){return hs()}static async getFocusedWindow(){for(const t of await hs())if(await t.isFocused())return t;return null}async listen(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:Cu(t,n,{target:{kind:"Window",label:this.label}})}async once(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:uh(t,n,{target:{kind:"Window",label:this.label}})}async emit(t,n){if(ms.includes(t)){for(const r of this.listeners[t]||[])r({event:t,id:-1,payload:n});return}return ch(t,n)}async emitTo(t,n,r){if(ms.includes(n)){for(const o of this.listeners[n]||[])o({event:n,id:-1,payload:r});return}return dh(t,n,r)}_handleTauriEvent(t,n){return ms.includes(t)?(t in this.listeners?this.listeners[t].push(n):this.listeners[t]=[n],!0):!1}async scaleFactor(){return T("plugin:window|scale_factor",{label:this.label})}async innerPosition(){return T("plugin:window|inner_position",{label:this.label}).then(t=>new tt(t))}async outerPosition(){return T("plugin:window|outer_position",{label:this.label}).then(t=>new tt(t))}async innerSize(){return T("plugin:window|inner_size",{label:this.label}).then(t=>new cr(t))}async outerSize(){return T("plugin:window|outer_size",{label:this.label}).then(t=>new cr(t))}async isFullscreen(){return T("plugin:window|is_fullscreen",{label:this.label})}async isMinimized(){return T("plugin:window|is_minimized",{label:this.label})}async isMaximized(){return T("plugin:window|is_maximized",{label:this.label})}async isFocused(){return T("plugin:window|is_focused",{label:this.label})}async isDecorated(){return T("plugin:window|is_decorated",{label:this.label})}async isResizable(){return T("plugin:window|is_resizable",{label:this.label})}async isMaximizable(){return T("plugin:window|is_maximizable",{label:this.label})}async isMinimizable(){return T("plugin:window|is_minimizable",{label:this.label})}async isClosable(){return T("plugin:window|is_closable",{label:this.label})}async isVisible(){return T("plugin:window|is_visible",{label:this.label})}async title(){return T("plugin:window|title",{label:this.label})}async theme(){return T("plugin:window|theme",{label:this.label})}async center(){return T("plugin:window|center",{label:this.label})}async requestUserAttention(t){let n=null;return t&&(t===ka.Critical?n={type:"Critical"}:n={type:"Informational"}),T("plugin:window|request_user_attention",{label:this.label,value:n})}async setResizable(t){return T("plugin:window|set_resizable",{label:this.label,value:t})}async setEnabled(t){return T("plugin:window|set_enabled",{label:this.label,value:t})}async isEnabled(){return T("plugin:window|is_enabled",{label:this.label})}async setMaximizable(t){return T("plugin:window|set_maximizable",{label:this.label,value:t})}async setMinimizable(t){return T("plugin:window|set_minimizable",{label:this.label,value:t})}async setClosable(t){return T("plugin:window|set_closable",{label:this.label,value:t})}async setTitle(t){return T("plugin:window|set_title",{label:this.label,value:t})}async maximize(){return T("plugin:window|maximize",{label:this.label})}async unmaximize(){return T("plugin:window|unmaximize",{label:this.label})}async toggleMaximize(){return T("plugin:window|toggle_maximize",{label:this.label})}async minimize(){return T("plugin:window|minimize",{label:this.label})}async unminimize(){return T("plugin:window|unminimize",{label:this.label})}async show(){return T("plugin:window|show",{label:this.label})}async hide(){return T("plugin:window|hide",{label:this.label})}async close(){return T("plugin:window|close",{label:this.label})}async destroy(){return T("plugin:window|destroy",{label:this.label})}async setDecorations(t){return T("plugin:window|set_decorations",{label:this.label,value:t})}async setShadow(t){return T("plugin:window|set_shadow",{label:this.label,value:t})}async setEffects(t){return T("plugin:window|set_effects",{label:this.label,value:t})}async clearEffects(){return T("plugin:window|set_effects",{label:this.label,value:null})}async setAlwaysOnTop(t){return T("plugin:window|set_always_on_top",{label:this.label,value:t})}async setAlwaysOnBottom(t){return T("plugin:window|set_always_on_bottom",{label:this.label,value:t})}async setContentProtected(t){return T("plugin:window|set_content_protected",{label:this.label,value:t})}async setSize(t){return T("plugin:window|set_size",{label:this.label,value:t instanceof Yt?t:new Yt(t)})}async setMinSize(t){return T("plugin:window|set_min_size",{label:this.label,value:t instanceof Yt?t:t?new Yt(t):null})}async setMaxSize(t){return T("plugin:window|set_max_size",{label:this.label,value:t instanceof Yt?t:t?new Yt(t):null})}async setSizeConstraints(t){function n(r){return r?{Logical:r}:null}return T("plugin:window|set_size_constraints",{label:this.label,value:{minWidth:n(t==null?void 0:t.minWidth),minHeight:n(t==null?void 0:t.minHeight),maxWidth:n(t==null?void 0:t.maxWidth),maxHeight:n(t==null?void 0:t.maxHeight)}})}async setPosition(t){return T("plugin:window|set_position",{label:this.label,value:t instanceof er?t:new er(t)})}async setFullscreen(t){return T("plugin:window|set_fullscreen",{label:this.label,value:t})}async setFocus(){return T("plugin:window|set_focus",{label:this.label})}async setIcon(t){return T("plugin:window|set_icon",{label:this.label,value:Ji(t)})}async setSkipTaskbar(t){return T("plugin:window|set_skip_taskbar",{label:this.label,value:t})}async setCursorGrab(t){return T("plugin:window|set_cursor_grab",{label:this.label,value:t})}async setCursorVisible(t){return T("plugin:window|set_cursor_visible",{label:this.label,value:t})}async setCursorIcon(t){return T("plugin:window|set_cursor_icon",{label:this.label,value:t})}async setBackgroundColor(t){return T("plugin:window|set_background_color",{color:t})}async setCursorPosition(t){return T("plugin:window|set_cursor_position",{label:this.label,value:t instanceof er?t:new er(t)})}async setIgnoreCursorEvents(t){return T("plugin:window|set_ignore_cursor_events",{label:this.label,value:t})}async startDragging(){return T("plugin:window|start_dragging",{label:this.label})}async startResizeDragging(t){return T("plugin:window|start_resize_dragging",{label:this.label,value:t})}async setBadgeCount(t){return T("plugin:window|set_badge_count",{label:this.label,value:t})}async setBadgeLabel(t){return T("plugin:window|set_badge_label",{label:this.label,value:t})}async setOverlayIcon(t){return T("plugin:window|set_overlay_icon",{label:this.label,value:t?Ji(t):void 0})}async setProgressBar(t){return T("plugin:window|set_progress_bar",{label:this.label,value:t})}async setVisibleOnAllWorkspaces(t){return T("plugin:window|set_visible_on_all_workspaces",{label:this.label,value:t})}async setTitleBarStyle(t){return T("plugin:window|set_title_bar_style",{label:this.label,value:t})}async setTheme(t){return T("plugin:window|set_theme",{label:this.label,value:t})}async onResized(t){return this.listen(xe.WINDOW_RESIZED,n=>{n.payload=new cr(n.payload),t(n)})}async onMoved(t){return this.listen(xe.WINDOW_MOVED,n=>{n.payload=new tt(n.payload),t(n)})}async onCloseRequested(t){return this.listen(xe.WINDOW_CLOSE_REQUESTED,async n=>{const r=new xx(n);await t(r),r.isPreventDefault()||await this.destroy()})}async onDragDropEvent(t){const n=await this.listen(xe.DRAG_ENTER,l=>{t({...l,payload:{type:"enter",paths:l.payload.paths,position:new tt(l.payload.position)}})}),r=await this.listen(xe.DRAG_OVER,l=>{t({...l,payload:{type:"over",position:new tt(l.payload.position)}})}),o=await this.listen(xe.DRAG_DROP,l=>{t({...l,payload:{type:"drop",paths:l.payload.paths,position:new tt(l.payload.position)}})}),i=await this.listen(xe.DRAG_LEAVE,l=>{t({...l,payload:{type:"leave"}})});return()=>{n(),o(),r(),i()}}async onFocusChanged(t){const n=await this.listen(xe.WINDOW_FOCUS,o=>{t({...o,payload:!0})}),r=await this.listen(xe.WINDOW_BLUR,o=>{t({...o,payload:!1})});return()=>{n(),r()}}async onScaleChanged(t){return this.listen(xe.WINDOW_SCALE_FACTOR_CHANGED,t)}async onThemeChanged(t){return this.listen(xe.WINDOW_THEME_CHANGED,t)}}var Od;(function(e){e.AppearanceBased="appearanceBased",e.Light="light",e.Dark="dark",e.MediumLight="mediumLight",e.UltraDark="ultraDark",e.Titlebar="titlebar",e.Selection="selection",e.Menu="menu",e.Popover="popover",e.Sidebar="sidebar",e.HeaderView="headerView",e.Sheet="sheet",e.WindowBackground="windowBackground",e.HudWindow="hudWindow",e.FullScreenUI="fullScreenUI",e.Tooltip="tooltip",e.ContentBackground="contentBackground",e.UnderWindowBackground="underWindowBackground",e.UnderPageBackground="underPageBackground",e.Mica="mica",e.Blur="blur",e.Acrylic="acrylic",e.Tabbed="tabbed",e.TabbedDark="tabbedDark",e.TabbedLight="tabbedLight"})(Od||(Od={}));var Ld;(function(e){e.FollowsWindowActiveState="followsWindowActiveState",e.Active="active",e.Inactive="inactive"})(Ld||(Ld={}));function _x(){return new eg(qm(),window.__TAURI_INTERNALS__.metadata.currentWebview.label,{skip:!0})}async function Dd(){return T("plugin:webview|get_all_webviews").then(e=>e.map(t=>new eg(new $u(t.windowLabel,{skip:!0}),t.label,{skip:!0})))}const gs=["tauri://created","tauri://error"];class eg{constructor(t,n,r){this.window=t,this.label=n,this.listeners=Object.create(null),r!=null&&r.skip||T("plugin:webview|create_webview",{windowLabel:t.label,label:n,options:r}).then(async()=>this.emit("tauri://created")).catch(async o=>this.emit("tauri://error",o))}static async getByLabel(t){var n;return(n=(await Dd()).find(r=>r.label===t))!==null&&n!==void 0?n:null}static getCurrent(){return _x()}static async getAll(){return Dd()}async listen(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:Cu(t,n,{target:{kind:"Webview",label:this.label}})}async once(t,n){return this._handleTauriEvent(t,n)?()=>{const r=this.listeners[t];r.splice(r.indexOf(n),1)}:uh(t,n,{target:{kind:"Webview",label:this.label}})}async emit(t,n){if(gs.includes(t)){for(const r of this.listeners[t]||[])r({event:t,id:-1,payload:n});return}return ch(t,n)}async emitTo(t,n,r){if(gs.includes(n)){for(const o of this.listeners[n]||[])o({event:n,id:-1,payload:r});return}return dh(t,n,r)}_handleTauriEvent(t,n){return gs.includes(t)?(t in this.listeners?this.listeners[t].push(n):this.listeners[t]=[n],!0):!1}async position(){return T("plugin:webview|webview_position",{label:this.label}).then(t=>new tt(t))}async size(){return T("plugin:webview|webview_size",{label:this.label}).then(t=>new cr(t))}async close(){return T("plugin:webview|close",{label:this.label})}async setSize(t){return T("plugin:webview|set_webview_size",{label:this.label,value:t instanceof Yt?t:new Yt(t)})}async setPosition(t){return T("plugin:webview|set_webview_position",{label:this.label,value:t instanceof er?t:new er(t)})}async setFocus(){return T("plugin:webview|set_webview_focus",{label:this.label})}async hide(){return T("plugin:webview|webview_hide",{label:this.label})}async show(){return T("plugin:webview|webview_show",{label:this.label})}async setZoom(t){return T("plugin:webview|set_webview_zoom",{label:this.label,value:t})}async reparent(t){return T("plugin:webview|reparent",{label:this.label,window:typeof t=="string"?t:t.label})}async clearAllBrowsingData(){return T("plugin:webview|clear_all_browsing_data")}async setBackgroundColor(t){return T("plugin:webview|set_webview_background_color",{color:t})}async onDragDropEvent(t){const n=await this.listen(xe.DRAG_ENTER,l=>{t({...l,payload:{type:"enter",paths:l.payload.paths,position:new tt(l.payload.position)}})}),r=await this.listen(xe.DRAG_OVER,l=>{t({...l,payload:{type:"over",position:new tt(l.payload.position)}})}),o=await this.listen(xe.DRAG_DROP,l=>{t({...l,payload:{type:"drop",paths:l.payload.paths,position:new tt(l.payload.position)}})}),i=await this.listen(xe.DRAG_LEAVE,l=>{t({...l,payload:{type:"leave"}})});return()=>{n(),o(),r(),i()}}}const Cx={theme:"system",setTheme:()=>null},Ex=g.createContext(Cx);function Vx({children:e,defaultTheme:t="system",storageKey:n="vite-ui-theme",...r}){const[o,i]=g.useState(()=>localStorage.getItem(n)||t);g.useEffect(()=>{const s=window.document.documentElement;if(s.classList.remove("light","dark"),o==="system"){const a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s.classList.add(a);return}s.classList.add(o)},[o]);const l={theme:o,setTheme:s=>{localStorage.setItem(n,s),i(s)}};return N.jsx(Ex.Provider,{...r,value:l,children:e})}export{X1 as $,fa as A,gl as B,bx as C,ro as D,Zp as E,kh as F,st as G,Pu as H,cw as I,Yw as J,zx as K,Mx as L,IS as M,fh as N,Nt as O,Nx as P,MS as Q,Xp as R,yr as S,Ix as T,jt as U,Ox as V,FS as W,sS as X,hw as Y,Er as Z,n0 as _,Ky as a,$1 as a0,J1 as a1,q1 as a2,tS as a3,nS as a4,oS as a5,rS as a6,Te as a7,eS as a8,Ny as a9,jx as aa,fx as ab,Ux as ac,px as ad,mx as ae,vh as af,$x as ag,Bx as ah,jS as ai,Rd as aj,Oy as ak,Iy as al,Ly as am,$u as an,eg as ao,Dy as ap,zy as aq,Ty as ar,Tx as as,Vx as at,Ku as au,_x as av,uh as aw,kx as ax,Id as ay,lw as b,wi as c,Ax as d,Ay as e,Rx as f,oo as g,Eu as h,T as i,N as j,kt as k,Cu as l,de as m,ml as n,pe as o,ee as p,Ru as q,g as r,$e as s,Dh as t,rw as u,s1 as v,Px as w,x0 as x,bu as y,Lx as z};
