export interface BitDefinition {
  mask: number;
  label: string;
}

export type VarType = 'u8' | 'u16' | 'u24' | 'bitmask';

export interface SaveVarDefinition {
  offset: number;
  length: number; // bytes
  type: VarType;
  label: string;
  description?: string;
  bits?: BitDefinition[];
}

export const bank1Fields: SaveVarDefinition[] = [
  { offset: 0, length: 2, type: 'u16', label: 'Story progression variable', description: 'Main progress variable tracking game story advancement' },
  { offset: 2, length: 1, type: 'u8', label: "<PERSON><PERSON><PERSON>'s initial level", description: 'Set when <PERSON><PERSON><PERSON> joins the team, prevents recruitment if changed before joining' },
  { offset: 3, length: 1, type: 'u8', label: "Aeris love points" },
  { offset: 4, length: 1, type: 'u8', label: "Tifa love points" },
  { offset: 5, length: 1, type: 'u8', label: "<PERSON><PERSON><PERSON> love points" },
  { offset: 6, length: 1, type: 'u8', label: "Barret love points" },
  { offset: 7, length: 1, type: 'u8', label: 'Temporary party member 1', description: 'Placeholder for party configuration during special events' },
  { offset: 8, length: 1, type: 'u8', label: 'Temporary party member 2', description: 'Placeholder for party configuration during special events' },
  { offset: 9, length: 1, type: 'u8', label: 'Temporary party member 3', description: 'Placeholder for party configuration during special events' },
  { offset: 16, length: 1, type: 'u8', label: 'Game timer hours' },
  { offset: 17, length: 1, type: 'u8', label: 'Game timer minutes' },
  { offset: 18, length: 1, type: 'u8', label: 'Game timer seconds' },
  { offset: 19, length: 1, type: 'u8', label: 'Game timer frames', description: 'From 0x00 to ~0x21 in one second (33 FPS)' },
  { offset: 20, length: 1, type: 'u8', label: 'Countdown timer hours' },
  { offset: 21, length: 1, type: 'u8', label: 'Countdown timer minutes' },
  { offset: 22, length: 1, type: 'u8', label: 'Countdown timer seconds' },
  { offset: 23, length: 1, type: 'u8', label: 'Countdown timer frames', description: 'From 0 to 30 (decimal) in one second' },
  { offset: 24, length: 2, type: 'u16', label: 'Total battles fought' },
  { offset: 26, length: 2, type: 'u16', label: 'Battle escapes count' },
  {
    offset: 28,
    length: 2,
    type: 'bitmask',
    label: 'Menu visibility flags',
    description: 'Controls which menu options are visible (Quit not affected)',
    bits: [
      { mask: 0x1, label: 'Item' },
      { mask: 0x2, label: 'Magic' },
      { mask: 0x4, label: 'Materia' },
      { mask: 0x8, label: 'Equip' },
      { mask: 0x10, label: 'Status' },
      { mask: 0x20, label: 'Order' },
      { mask: 0x40, label: 'Limit' },
      { mask: 0x80, label: 'Config' },
      { mask: 0x100, label: 'PHS' },
      { mask: 0x200, label: 'Save' },
    ],
  },
  {
    offset: 30,
    length: 2,
    type: 'bitmask',
    label: 'Menu lock flags',
    description: 'Controls which menu options are locked (1: Locked, Quit not affected)',
    bits: [
      { mask: 0x1, label: 'Item' },
      { mask: 0x2, label: 'Magic' },
      { mask: 0x4, label: 'Materia' },
      { mask: 0x8, label: 'Equip' },
      { mask: 0x10, label: 'Status' },
      { mask: 0x20, label: 'Order' },
      { mask: 0x40, label: 'Limit' },
      { mask: 0x80, label: 'Config' },
      { mask: 0x100, label: 'PHS' },
      { mask: 0x200, label: 'Save' },
    ],
  },
  {
    offset: 36,
    length: 1,
    type: 'bitmask',
    label: 'Sector 7 Train Graveyard items',
    bits: [
      { mask: 0x01, label: 'Hi-Potion (Barrel 1)' },
      { mask: 0x02, label: 'Echo Screen (Barrel 2)' },
      { mask: 0x04, label: 'Potion (Floor 2)' },
      { mask: 0x08, label: 'Ether (Floor 3)' },
      { mask: 0x10, label: 'Hi-Potion (Roof Train 1)' },
      { mask: 0x20, label: 'Potion (Inside Train 2)' },
      { mask: 0x40, label: 'Potion (Floor 1)' },
      { mask: 0x80, label: 'Hi-Potion (Roof Train 2)' },
    ],
  },
  {
    offset: 37,
    length: 1,
    type: 'bitmask',
    label: 'Miscellaneous field items',
    bits: [
      { mask: 0x01, label: 'Elixir' },
      { mask: 0x02, label: 'Potion' },
      { mask: 0x04, label: 'Safety Bit' },
      { mask: 0x08, label: 'Mind Source' },
      { mask: 0x10, label: 'Sneak Glove' },
      { mask: 0x20, label: 'Premium Heart' },
    ],
  },
  {
    offset: 48,
    length: 1,
    type: 'bitmask',
    label: 'Field items and materia',
    bits: [
      { mask: 0x01, label: 'Potion' },
      { mask: 0x02, label: 'Potion + Phoenix Down' },
      { mask: 0x04, label: 'Ether' },
      { mask: 0x08, label: 'Cover materia' },
      { mask: 0x10, label: 'Choco-Mog summon' },
      { mask: 0x20, label: 'Sense materia' },
      { mask: 0x40, label: 'Ramuh summon' },
      { mask: 0x80, label: 'Mythril key' },
    ],
  },
  {
    offset: 49,
    length: 1,
    type: 'bitmask',
    label: 'Materia/Northern Cave items',
    bits: [
      { mask: 0x01, label: 'Mime materia' },
      { mask: 0x02, label: 'HP<->MP materia' },
      { mask: 0x04, label: 'Quadra Magic materia' },
      { mask: 0x08, label: 'Knights of the Round' },
      { mask: 0x10, label: 'Elixir' },
      { mask: 0x20, label: 'X-Potion' },
      { mask: 0x40, label: 'Turbo Ether' },
      { mask: 0x80, label: 'Vaccine' },
    ],
  },
  {
    offset: 50,
    length: 1,
    type: 'bitmask',
    label: 'Northern Cave items 1',
    bits: [
      { mask: 0x01, label: 'Magic Counter materia' },
      { mask: 0x02, label: 'Speed Source' },
      { mask: 0x04, label: 'Turbo Ether (2)' },
      { mask: 0x08, label: 'X-Potion (2)' },
      { mask: 0x10, label: 'Mega All materia' },
      { mask: 0x20, label: 'Luck Source' },
      { mask: 0x40, label: 'Remedy' },
      { mask: 0x80, label: 'Bolt Ring' },
    ],
  },
  {
    offset: 51,
    length: 1,
    type: 'bitmask',
    label: 'Northern Cave items 2',
    bits: [
      { mask: 0x01, label: 'Gold Armlet' },
      { mask: 0x02, label: 'Great Gospel limit' },
      { mask: 0x04, label: 'Shooting Coaster Umbrella prize' },
      { mask: 0x08, label: 'Shooting Coaster Flayer prize' },
      { mask: 0x10, label: 'Death Penalty + Chaos' },
      { mask: 0x20, label: 'Elixir (2)' },
      { mask: 0x40, label: 'Enemy Skill animation displayed' },
      { mask: 0x80, label: 'Enemy Skill materia' },
    ],
  },
  {
    offset: 56,
    length: 1,
    type: 'bitmask',
    label: 'Wall Market & Shinra items',
    bits: [
      { mask: 0x01, label: 'Ether (Corneo basement)' },
      { mask: 0x02, label: 'Hyper (Corneo bedroom)' },
      { mask: 0x04, label: 'Phoenix Down (Corneo 2nd floor)' },
      { mask: 0x08, label: 'Elixir (Shinra HQ stairs)' },
      { mask: 0x20, label: 'Magic Source' },
      { mask: 0x40, label: 'First Midgar Part' },
      { mask: 0x80, label: 'Second Midgar Part' },
    ],
  },
  {
    offset: 57,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ items',
    bits: [
      { mask: 0x01, label: 'Third Midgar Part' },
      { mask: 0x02, label: 'Fourth Midgar Part' },
      { mask: 0x04, label: 'Fifth Midgar Part' },
      { mask: 0x08, label: 'Keycard 66' },
      { mask: 0x10, label: 'All materia' },
      { mask: 0x20, label: 'Ether' },
      { mask: 0x40, label: 'Wind Slash weapon' },
      { mask: 0x80, label: 'Fairy Ring accessory' },
    ],
  },
  {
    offset: 58,
    length: 1,
    type: 'bitmask',
    label: 'Various field items 1',
    bits: [
      { mask: 0x01, label: 'X-Potion' },
      { mask: 0x02, label: 'Added Effect materia' },
      { mask: 0x04, label: 'Black M-phone' },
      { mask: 0x08, label: 'Ether' },
      { mask: 0x10, label: 'Elixir' },
      { mask: 0x20, label: 'HP Absorb materia' },
      { mask: 0x40, label: 'Magic Shuriken' },
      { mask: 0x80, label: 'Hairpin accessory' },
    ],
  },
  {
    offset: 59,
    length: 1,
    type: 'bitmask',
    label: 'Various field items 2',
    bits: [
      { mask: 0x01, label: 'Keycard 62' },
      { mask: 0x02, label: 'MP Absorb materia' },
      { mask: 0x04, label: 'Swift Bolt weapon' },
      { mask: 0x08, label: 'Elixir' },
      { mask: 0x10, label: 'Pile Bunker weapon' },
      { mask: 0x20, label: 'Master Fist weapon' },
      { mask: 0x40, label: 'Behemoth Horn item' },
      { mask: 0x80, label: 'Full Cure materia' },
    ],
  },
  { offset: 64, length: 8, type: 'u8', label: 'Key items inventory', description: 'Storage for various key items needed for progression' },
  { offset: 72, length: 1, type: 'u8', label: 'Northern Cave progress 1', description: 'Progress tracking for Northern Cave area' },
  { offset: 74, length: 1, type: 'u8', label: 'Northern Cave progress 2', description: 'Additional progress tracking for Northern Cave area' },
  {
    offset: 75,
    length: 1,
    type: 'bitmask',
    label: 'Chocobo Farm obtained items',
    bits: [
      { mask: 0x01, label: 'Choco-Mog obtained' },
      { mask: 0x02, label: 'Enemy Skill obtained' },
    ],
  },
  { offset: 80, length: 1, type: 'u8', label: 'Aeris battle love points', description: 'Love points gained through battle choices' },
  { offset: 81, length: 1, type: 'u8', label: 'Tifa battle love points', description: 'Love points gained through battle choices' },
  { offset: 82, length: 1, type: 'u8', label: 'Yuffie battle love points', description: 'Love points gained through battle choices' },
  { offset: 83, length: 1, type: 'u8', label: 'Barret battle love points', description: 'Love points gained through battle choices' },
  { offset: 85, length: 1, type: 'u8', label: 'Chocobo 1 rating', description: 'Rating for penned chocobo (01: Wonderful -> 08: Worst)' },
  { offset: 86, length: 1, type: 'u8', label: 'Chocobo 2 rating', description: 'Rating for penned chocobo (01: Wonderful -> 08: Worst)' },
  { offset: 87, length: 1, type: 'u8', label: 'Chocobo 3 rating', description: 'Rating for penned chocobo (01: Wonderful -> 08: Worst)' },
  { offset: 88, length: 1, type: 'u8', label: 'Chocobo 4 rating', description: 'Rating for penned chocobo (01: Wonderful -> 08: Worst)' },
  { offset: 91, length: 3, type: 'u24', label: "Ultimate Weapon's remaining HP" },
  { offset: 94, length: 1, type: 'bitmask', label: 'Northern Cave battle flags', bits: [
    { mask: 0x01, label: 'Dragon Zombie used Pandora\'s Box' },
    { mask: 0x04, label: 'Northern Cave progress flag' },
    { mask: 0x20, label: 'Bizarro - second party switch' },
    { mask: 0x40, label: 'Bizarro - first party switch' },
    { mask: 0x80, label: 'Bizarro battle started' },
  ]},
  { offset: 112, length: 2, type: 'u16', label: 'Current Battle Square points' },
  { offset: 116, length: 1, type: 'u8', label: 'Battle Square battles won' },
  {
    offset: 122,
    length: 1,
    type: 'bitmask',
    label: 'Tutorial and event flags',
    description: 'Controls tutorial display and various event triggers',
    bits: [
      { mask: 0x01, label: 'Junon Parade event' },
      { mask: 0x02, label: 'Space animation displayed' },
      { mask: 0x04, label: 'Grey Submarine tutorial seen' },
      { mask: 0x08, label: 'Forgotten City animation displayed' },
      { mask: 0x20, label: 'Snow Area tutorial seen' },
      { mask: 0x40, label: 'Display field help' },
      { mask: 0x80, label: 'Bizarro main group fighting' },
    ],
  },
  {
    offset: 123,
    length: 1,
    type: 'bitmask',
    label: 'Weapon boss defeats',
    bits: [
      { mask: 0x01, label: 'Ultimate Weapon killed' },
      { mask: 0x04, label: 'Ultima Weapon HP < 20,000' },
      { mask: 0x08, label: 'Ruby Weapon defeated' },
      { mask: 0x10, label: 'Emerald Weapon defeated' },
    ],
  },
  { offset: 124, length: 1, type: 'u8', label: 'Chocobo taken from stable', description: 'Which stable chocobo was taken (00-06), affects display/availability' },
  { offset: 125, length: 1, type: 'u8', label: 'Wild chocobo dialog options', description: 'Controls direction prompts and send/release options when riding' },
  {
    offset: 126,
    length: 1,
    type: 'bitmask',
    label: 'World map chocobo display',
    bits: [
      { mask: 0x01, label: 'Caught wild chocobo' },
      { mask: 0x02, label: 'Riding chocobo' },
      { mask: 0x04, label: 'Yellow chocobo' },
      { mask: 0x08, label: 'Green chocobo' },
      { mask: 0x10, label: 'Blue chocobo' },
      { mask: 0x20, label: 'Black chocobo' },
      { mask: 0x40, label: 'Gold chocobo' },
    ],
  },
  {
    offset: 127,
    length: 1,
    type: 'bitmask',
    label: 'World map vehicle display',
    description: 'Controls which vehicles appear on world map (buggy invisible if not disk 1)',
    bits: [
      { mask: 0x01, label: 'Buggy available' },
      { mask: 0x02, label: 'Driving buggy' },
      { mask: 0x04, label: 'Tiny Bronco available' },
      { mask: 0x10, label: 'Highwind available' },
      { mask: 0x20, label: 'Highwind flying' },
    ],
  },
  {
    offset: 128,
    length: 1,
    type: 'bitmask',
    label: 'Corel area story flags',
    bits: [
      { mask: 0x01, label: 'Barret talks about hometown' },
      { mask: 0x02, label: 'Free rest in Corel inn' },
      { mask: 0x04, label: 'Villagers rebuke Barret' },
      { mask: 0x20, label: 'Couldn\'t stop the train' },
      { mask: 0x40, label: 'Huge Materia key item' },
      { mask: 0x80, label: 'Ultima materia obtained' },
    ],
  },
  {
    offset: 129,
    length: 1,
    type: 'bitmask',
    label: 'Junon area story flags',
    bits: [
      { mask: 0x01, label: 'Priscilla warnings given' },
      { mask: 0x02, label: 'Oldman: "Do CPR!"' },
      { mask: 0x04, label: 'Free rest offer made' },
      { mask: 0x08, label: 'Talk about black cape man' },
      { mask: 0x10, label: 'Priscilla: "Gets deeper..."' },
      { mask: 0x20, label: 'Tifa: "5 years ago"' },
      { mask: 0x40, label: 'Cloud: "Hey!!" (climb tower)' },
      { mask: 0x80, label: 'Reached top of pole' },
    ],
  },
  {
    offset: 130,
    length: 1,
    type: 'bitmask',
    label: 'Snow area story flags',
    bits: [
      { mask: 0x01, label: 'Man1: "It\'s dangerous!"' },
      { mask: 0x02, label: 'Snowboard key item obtained' },
      { mask: 0x04, label: 'Kicked Shinra soldier' },
      { mask: 0x08, label: 'Elena punched Cloud' },
      { mask: 0x10, label: 'Cloud woke in Gast home' },
      { mask: 0x20, label: 'Boy gave snowboard' },
      { mask: 0x40, label: 'Glacier Map key item' },
      { mask: 0x80, label: 'First time snowboarding' },
    ],
  },
  {
    offset: 131,
    length: 1,
    type: 'bitmask',
    label: 'Great Glacier story flags',
    bits: [
      { mask: 0x01, label: 'Successfully climbed to top' },
      { mask: 0x02, label: 'Pushed over the rock' },
      { mask: 0x04, label: 'Ice Pillar 1 destroyed' },
      { mask: 0x08, label: 'Ice Pillar 2 destroyed' },
      { mask: 0x10, label: 'Ice Pillar 3 destroyed' },
      { mask: 0x20, label: 'Ice Pillar 4 destroyed' },
      { mask: 0x40, label: 'First Holzoff house visit' },
      { mask: 0x80, label: 'Yamski history & cliff tutorial' },
    ],
  },
  {
    offset: 132,
    length: 1,
    type: 'bitmask',
    label: 'Crater area story flags',
    bits: [
      { mask: 0x01, label: 'First time GAIAFOOT map' },
      { mask: 0x04, label: 'Tifa asks to come along' },
      { mask: 0x08, label: 'Rufus finds crater' },
      { mask: 0x10, label: 'Sephiroth: "This is the end"' },
      { mask: 0x40, label: 'Sephiroth illusion Nibelheim' },
      { mask: 0x80, label: 'First crater_1 conversation' },
    ],
  },
  {
    offset: 133,
    length: 1,
    type: 'bitmask',
    label: 'Whirlwind Maze flags 1',
    bits: [
      { mask: 0x01, label: 'Gave Black Materia to Barret' },
      { mask: 0x02, label: 'Gave Black Materia to Red XIII' },
      { mask: 0x04, label: 'Entrusted Black Materia' },
      { mask: 0x08, label: 'Tifa: cross when calm' },
      { mask: 0x10, label: '12 Black Cape men defeated' },
      { mask: 0x20, label: 'Black Cape man defeated' },
      { mask: 0x40, label: 'Black Cape man down (crater)' },
      { mask: 0x80, label: 'Black Cape man jumped off' },
    ],
  },
  {
    offset: 134,
    length: 1,
    type: 'bitmask',
    label: 'Whirlwind Maze flags 2',
    bits: [
      { mask: 0x01, label: 'Cloud: "What happened to town?"' },
      { mask: 0x02, label: 'Already torn down map' },
      { mask: 0x04, label: 'Screen shake then battle' },
      { mask: 0x08, label: 'Shiva Summon in Priscilla house' },
      { mask: 0x10, label: 'Black Cape man: "Ugh... Errgaahh!!"' },
      { mask: 0x20, label: 'Oldm1: "Cloud is missing?"' },
      { mask: 0x40, label: 'Priscilla/Tifa talk (Cloud missing)' },
      { mask: 0x80, label: 'Priscilla/Cloud talk (after lifestream)' },
    ],
  },
  {
    offset: 135,
    length: 1,
    type: 'bitmask',
    label: 'Whirlwind Maze flags 3',
    bits: [
      { mask: 0x01, label: 'Cid regrets about Huge Materia' },
      { mask: 0x02, label: 'After Barret hometown history' },
      { mask: 0x04, label: 'Cid tells Priscilla found Cloud' },
    ],
  },
  {
    offset: 160,
    length: 1,
    type: 'bitmask',
    label: 'Wall Market disguise items 1',
    description: 'Items for Cloud\'s disguise quest in Wall Market',
    bits: [
      { mask: 0x01, label: 'Cologne obtained' },
      { mask: 0x02, label: 'Flower Cologne obtained' },
      { mask: 0x04, label: 'Sexy Cologne obtained' },
      { mask: 0x08, label: 'Wig obtained' },
      { mask: 0x10, label: 'Dyed Wig obtained' },
      { mask: 0x20, label: 'Blonde Wig obtained' },
      { mask: 0x40, label: 'Pharmacy coupon obtained' },
      { mask: 0x80, label: 'Any wig obtained' },
    ],
  },
  {
    offset: 161,
    length: 1,
    type: 'bitmask',
    label: 'Wall Market disguise items 2',
    description: 'More items for Cloud\'s disguise quest',
    bits: [
      { mask: 0x01, label: 'Poor make-up result' },
      { mask: 0x02, label: 'Average make-up result' },
      { mask: 0x04, label: 'Best make-up result' },
      { mask: 0x08, label: 'Obtaining dress process' },
      { mask: 0x10, label: 'Dress selected' },
      { mask: 0x20, label: 'Cotton Dress obtained' },
      { mask: 0x40, label: 'Satin Dress obtained' },
      { mask: 0x80, label: 'Silk Dress obtained' },
    ],
  },
  {
    offset: 162,
    length: 1,
    type: 'bitmask',
    label: 'Wall Market disguise items 3',
    description: 'Additional items for Wall Market quests',
    bits: [
      { mask: 0x01, label: 'Disinfectant obtained' },
      { mask: 0x02, label: 'Deodorant obtained' },
      { mask: 0x04, label: 'Digestive obtained' },
      { mask: 0x08, label: 'Materia shop owner request' },
      { mask: 0x10, label: '200 gil vending item' },
      { mask: 0x20, label: '100 gil vending item' },
      { mask: 0x40, label: '50 gil vending item' },
      { mask: 0x80, label: 'Boutique son request made' },
    ],
  },
  { offset: 163, length: 1, type: 'u8', label: 'Ms. Cloud beauty level', description: 'Beauty points (0-25) determining Don Corneo\'s choice' },
  {
    offset: 164,
    length: 1,
    type: 'bitmask',
    label: 'Train Graveyard train positions',
    bits: [
      { mask: 0x01, label: 'Train 1 moved' },
      { mask: 0x02, label: 'Train 2 moved' },
      { mask: 0x04, label: 'Train 3 moved' },
    ],
  },
  {
    offset: 165,
    length: 1,
    type: 'bitmask',
    label: 'Wall Market climbing puzzle',
    description: 'Battery puzzle progress for climbing Wall Market',
    bits: [
      { mask: 0x01, label: 'First battery holder seen' },
      { mask: 0x02, label: 'First battery applied' },
      { mask: 0x04, label: 'Second battery applied' },
      { mask: 0x10, label: 'Third battery + Ether' },
      { mask: 0x20, label: 'Battery pack 1/3' },
      { mask: 0x40, label: 'Battery pack 2/3' },
      { mask: 0x80, label: 'Battery pack 3/3' },
    ],
  },
  { offset: 166, length: 1, type: 'u8', label: 'Fort Condor battles fought' },
  { offset: 167, length: 1, type: 'u8', label: 'Fort Condor battles won' },
  {
    offset: 168,
    length: 1,
    type: 'bitmask',
    label: 'Fort Condor story progress 1',
    bits: [
      { mask: 0x01, label: 'Oldman asks for help' },
      { mask: 0x02, label: 'Cloud joins fight' },
      { mask: 0x04, label: 'Additional services unlocked' },
      { mask: 0x08, label: 'No more Shinra troops' },
      { mask: 0x10, label: 'Banished after losing Huge Materia' },
      { mask: 0x20, label: 'Phoenix materia obtained' },
      { mask: 0x40, label: 'Condor born movie seen' },
      { mask: 0x80, label: 'Rope cut after losing' },
    ],
  },
  { offset: 169, length: 1, type: 'u8', label: 'Fort Condor battle rank', description: 'Battle difficulty rank (1-6), affects enemy count and types' },
  { offset: 170, length: 1, type: 'u8', label: 'Fort Condor allies remaining', description: 'Number of surviving ally units' },
  { offset: 171, length: 1, type: 'u8', label: 'Fort Condor enemies killed' },
  { offset: 172, length: 1, type: 'u8', label: 'Fort Condor battle result', description: '0: Victory, 1: Defeat' },
  { offset: 173, length: 2, type: 'u16', label: 'Fort Condor progress temp', description: 'Temporary variable for tracking progression changes' },
  { offset: 175, length: 1, type: 'u8', label: 'Fort Condor enemies alive', description: 'Enemies remaining on battlefield (set by kernel)' },
  { offset: 176, length: 1, type: 'u8', label: 'Fort Condor battle type', description: '0: No battle, 1: Normal, 3: Final boss' },
  {
    offset: 177,
    length: 1,
    type: 'bitmask',
    label: 'Fort Condor progress & rewards',
    bits: [
      { mask: 0x01, label: 'Condor Progress 1' },
      { mask: 0x02, label: 'Condor Progress 2' },
      { mask: 0x04, label: 'Condor Progress 3' },
      { mask: 0x08, label: 'Condor Progress 4' },
      { mask: 0x10, label: 'Magic Comb received' },
      { mask: 0x20, label: 'Peace Ring received' },
      { mask: 0x40, label: 'Megalixir received' },
      { mask: 0x80, label: 'Super Ball received' },
    ],
  },
  {
    offset: 178,
    length: 1,
    type: 'bitmask',
    label: 'Fort Condor access flags',
    bits: [
      { mask: 0x02, label: 'Ever entered condor1 map' },
      { mask: 0x04, label: 'Inside the Fort' },
    ],
  },
  {
    offset: 179,
    length: 1,
    type: 'bitmask',
    label: 'Fort Condor battle modifiers',
    description: 'Battle difficulty modifiers based on rank',
    bits: [
      { mask: 0x01, label: 'Battle rank >= 2' },
      { mask: 0x02, label: 'Battle rank >= 3' },
    ],
  },
  { offset: 180, length: 2, type: 'u16', label: 'Fort Condor funds available' },
  { offset: 182, length: 1, type: 'u8', label: 'Fort Condor battles lost' },
  {
    offset: 183,
    length: 1,
    type: 'bitmask',
    label: 'Wall Market NPC conversations',
    bits: [
      { mask: 0x01, label: 'Children near wall 1' },
      { mask: 0x02, label: 'Children near wall 2' },
      { mask: 0x04, label: 'Children on top of wall' },
      { mask: 0x08, label: 'Child by pipe' },
    ],
  },
  { offset: 184, length: 1, type: 'u8', label: 'Great Glacier map jumps', description: 'Handler for map transitions in Great Glacier area' },
  {
    offset: 185,
    length: 1,
    type: 'bitmask',
    label: 'Great Glacier entry method',
    bits: [
      { mask: 0x01, label: 'Not from snowboard (inverted)' },
      { mask: 0x02, label: 'From Glacier Map screen' },
    ],
  },
  { offset: 186, length: 2, type: 'u16', label: 'Stored Cloud map ID', description: 'Great Glacier position restoration data' },
  { offset: 188, length: 2, type: 'u16', label: 'Stored Cloud X position', description: 'Great Glacier position restoration data' },
  { offset: 190, length: 2, type: 'u16', label: 'Stored Cloud Y position', description: 'Great Glacier position restoration data' },
  { offset: 192, length: 2, type: 'u16', label: 'Stored Cloud Z position', description: 'Great Glacier position restoration data' },
  { offset: 194, length: 2, type: 'u16', label: 'Stored Cloud triangle ID', description: 'Great Glacier position restoration data' },
  { offset: 196, length: 1, type: 'u8', label: 'Stored Cloud direction', description: 'Great Glacier position restoration data' },
  { offset: 197, length: 1, type: 'u8', label: 'Cloud pass-out flag', description: 'Set to 1 when Cloud collapses in Great Glacier' },
  { offset: 198, length: 1, type: 'u8', label: 'Cloud pass-out counter', description: 'Tracks how many times Cloud has collapsed' },
  {
    offset: 199,
    length: 1,
    type: 'bitmask',
    label: 'Great Glacier progress & items',
    bits: [
      { mask: 0x01, label: 'Touched hot spring (Alexander)' },
      { mask: 0x02, label: 'Ever spoken to snoww' },
      { mask: 0x04, label: 'Lost to Snow woman' },
      { mask: 0x08, label: 'Won against Snow woman' },
      { mask: 0x10, label: 'Alexander materia received' },
      { mask: 0x20, label: 'Added Cut materia received' },
      { mask: 0x40, label: 'All materia received' },
    ],
  },
  { offset: 208, length: 1, type: 'u8', label: 'Debug room user answer', description: 'Saves debug room selection before map jump' },
  { offset: 209, length: 1, type: 'u8', label: 'Unknown Wall Market flag', description: 'Purpose unknown, set to 0 in Wall Market' },
  { offset: 224, length: 1, type: 'u8', label: 'Shinra floors unlocked', description: 'Tracks keycard progression (255 = all doors)' },
  {
    offset: 225,
    length: 1,
    type: 'bitmask',
    label: '1st Reactor mission flags 1',
    bits: [
      { mask: 0x01, label: 'Elevator on top floor' },
      { mask: 0x08, label: '1st door opened' },
      { mask: 0x10, label: '2nd door opened' },
      { mask: 0x20, label: 'Jessie freed from stuck' },
      { mask: 0x40, label: 'Bomb set' },
      { mask: 0x80, label: 'Time out for gameover' },
    ],
  },
  {
    offset: 226,
    length: 1,
    type: 'bitmask',
    label: '1st Reactor mission flags 2',
    bits: [
      { mask: 0x02, label: 'Elevator door opened' },
      { mask: 0x04, label: 'Scrolled to show reactor' },
    ],
  },
];

export const bank3Fields: SaveVarDefinition[] = [
  { offset: 9, length: 1, type: 'u8', label: '1st party member ID mirror', description: 'Mirror of field module party member 1 (0x04F8)' },
  { offset: 10, length: 1, type: 'u8', label: '2nd party member ID mirror', description: 'Mirror of field module party member 2 (0x04F9)' },
  { offset: 11, length: 1, type: 'u8', label: '3rd party member ID mirror', description: 'Mirror of field module party member 3 (0x04FA)' },
  { offset: 16, length: 1, type: 'u8', label: 'Aeris church progression' },
  {
    offset: 66,
    length: 1,
    type: 'bitmask',
    label: '1st reactor escape progress',
    bits: [
      { mask: 0x01, label: 'After scroll at MD8_2 start' },
      { mask: 0x02, label: 'People panic event finished' },
    ],
  },
  { offset: 74, length: 2, type: 'u16', label: 'Party GP', description: 'Gold Points (0-10000)' },
  { offset: 76, length: 1, type: 'u8', label: 'Corel Prison race losses', description: 'Times lost in Corel Prison chocobo race' },
  { offset: 79, length: 1, type: 'u8', label: 'Battle Square dialog progress', description: '0x00: init, 0x10: no text, 0xF0: new special fight' },
  { offset: 80, length: 2, type: 'u16', label: 'Battle Square battle points' },
  { offset: 88, length: 1, type: 'u8', label: 'Chocobo stables owned' },
  { offset: 89, length: 1, type: 'u8', label: 'Occupied stables count' },
  {
    offset: 90,
    length: 1,
    type: 'bitmask',
    label: 'Choco Bill dialog flags',
    bits: [
      { mask: 0x01, label: 'Post-meteor conversation shown' },
      { mask: 0x02, label: 'Stable rental offer shown' },
    ],
  },
  {
    offset: 91,
    length: 1,
    type: 'bitmask',
    label: 'Chocobo stables occupied',
    description: 'Occupied stable mask (LSB=Stable 1, bit 6=Stable 6)',
    bits: [
      { mask: 0x01, label: 'Stable 1 occupied' },
      { mask: 0x02, label: 'Stable 2 occupied' },
      { mask: 0x04, label: 'Stable 3 occupied' },
      { mask: 0x08, label: 'Stable 4 occupied' },
      { mask: 0x10, label: 'Stable 5 occupied' },
      { mask: 0x20, label: 'Stable 6 occupied' },
    ],
  },
  {
    offset: 92,
    length: 1,
    type: 'bitmask',
    label: 'Chocobos unable to mate',
    description: 'Recently born or mated chocobos (LSB=Stable 1, bit 6=Stable 6)',
    bits: [
      { mask: 0x01, label: 'Stable 1 chocobo' },
      { mask: 0x02, label: 'Stable 2 chocobo' },
      { mask: 0x04, label: 'Stable 3 chocobo' },
      { mask: 0x08, label: 'Stable 4 chocobo' },
      { mask: 0x10, label: 'Stable 5 chocobo' },
      { mask: 0x20, label: 'Stable 6 chocobo' },
    ],
  },
  {
    offset: 111,
    length: 1,
    type: 'bitmask',
    label: 'Aeris flower quest progress',
    bits: [
      { mask: 0x01, label: 'Bought flower from Aeris' },
    ],
  },
  { offset: 127, length: 1, type: 'u8', label: 'Tunnel room number', description: 'Current room in TUNNEL_1 (1-6), set during the Sector 5 train minigame' },
  {
    offset: 128,
    length: 1,
    type: 'bitmask',
    label: 'Kalm conversation flags',
    description: 'NPCs spoken to in Kalm',
    bits: [
      { mask: 0x08, label: 'Child in house next to inn' },
      { mask: 0x10, label: 'Freed the dog in house' },
    ],
  },
  {
    offset: 130,
    length: 1,
    type: 'bitmask',
    label: 'Reactor conversations 1',
    description: 'Conversations under the plate reactor',
    bits: [
      { mask: 0x01, label: 'Spoke with Biggs' },
    ],
  },
  {
    offset: 131,
    length: 1,
    type: 'bitmask',
    label: 'Reactor conversations 2',
    description: 'Conversations under the plate reactor',
    bits: [
      { mask: 0x01, label: 'Spoke with Jessie' },
    ],
  },
  { offset: 133, length: 1, type: 'u8', label: 'Yuffie forest encounter flag', description: 'Can Yuffie be found in forests (LSB only)' },
  {
    offset: 160,
    length: 1,
    type: 'bitmask',
    label: 'Reactor events & items',
    description: 'Various reactor events and item collection',
    bits: [
      { mask: 0x10, label: 'Aerith roof event ends' },
      { mask: 0x40, label: 'Turbo Ether obtained' },
      { mask: 0x80, label: 'Aerith roof event starts' },
    ],
  },
  { offset: 162, length: 1, type: 'u8', label: "Don's mission progress" },
  {
    offset: 163,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ conversations 1',
    bits: [
      { mask: 0x08, label: 'Elevator event at Shinra HQ' },
      { mask: 0x10, label: 'First stairs conversation' },
    ],
  },
  {
    offset: 165,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ conversations 2',
    bits: [
      { mask: 0x80, label: 'Second stairs conversation' },
    ],
  },
  {
    offset: 166,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ conversations 3',
    bits: [
      { mask: 0x01, label: 'Third stairs conversation' },
    ],
  },
  {
    offset: 168,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ events',
    bits: [
      { mask: 0x01, label: 'Breaking in scene' },
      { mask: 0x02, label: 'Guards defeated, keycard obtained' },
      { mask: 0x04, label: 'First floor cleared' },
      { mask: 0x08, label: 'Spoke to shop couple' },
      { mask: 0x10, label: 'Spoke to shop seller' },
      { mask: 0x20, label: 'Front door conversation 1' },
      { mask: 0x40, label: 'Front door conversation 2' },
    ],
  },
  {
    offset: 172,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ floor 64 items',
    bits: [
      { mask: 0x01, label: 'Phoenix Down from locker' },
      { mask: 0x02, label: 'Ether from locker' },
      { mask: 0x10, label: 'Floor 60 elevator FMV' },
    ],
  },
  { offset: 174, length: 2, type: 'u16', label: 'Shinra floor 63 door states', description: 'Door open/closed states (0: open, 1: closed)' },
  {
    offset: 177,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ coupons & events',
    bits: [
      { mask: 0x02, label: 'Coupon C obtained (1)' },
      { mask: 0x04, label: 'Coupon C obtained (2)' },
      { mask: 0x08, label: 'Coupon B obtained' },
      { mask: 0x10, label: 'Spoke to floor 63 machine' },
    ],
  },
  { offset: 178, length: 1, type: 'u8', label: 'Shinra floor 63 events', description: 'Floor 63 event states (needs investigation)' },
  {
    offset: 179,
    length: 1,
    type: 'bitmask',
    label: 'Shinra HQ vending machine',
    bits: [
      { mask: 0x01, label: 'Hit vending machine floor 64' },
    ],
  },
  {
    offset: 180,
    length: 1,
    type: 'bitmask',
    label: 'Midgar model assembly',
    bits: [
      { mask: 0x08, label: 'Fifth part placed' },
      { mask: 0x10, label: 'Fourth part placed' },
      { mask: 0x20, label: 'Third part placed' },
      { mask: 0x40, label: 'Second part placed' },
      { mask: 0x80, label: 'First part placed' },
    ],
  },
  {
    offset: 181,
    length: 1,
    type: 'bitmask',
    label: 'Midgar model completion',
    bits: [
      { mask: 0x01, label: 'Model lights up floor 65' },
      { mask: 0x80, label: 'Last machine conversation' },
    ],
  },
  {
    offset: 185,
    length: 1,
    type: 'bitmask',
    label: 'Coupon retrieval progress',
    bits: [
      { mask: 0x20, label: 'Coupon retrieval step 1' },
      { mask: 0x40, label: 'Coupon retrieval step 2' },
      { mask: 0x80, label: 'Coupon retrieval step 3' },
    ],
  },
  {
    offset: 194,
    length: 1,
    type: 'bitmask',
    label: 'Turtle Paradise flyers seen',
    bits: [
      { mask: 0x01, label: 'Sector 7 Slums' },
      { mask: 0x02, label: 'Shinra Building 1st Floor' },
      { mask: 0x04, label: 'Gold Saucer Ghost Hotel' },
      { mask: 0x08, label: 'Cosmo Canyon Inn 2nd Floor' },
      { mask: 0x10, label: 'Cosmo Canyon near Shop' },
      { mask: 0x20, label: 'Wutai trap room entrance' },
      { mask: 0x40, label: 'Wutai Turtle Paradise entrance' },
    ],
  },
  {
    offset: 195,
    length: 1,
    type: 'bitmask',
    label: 'Misc conversation flags',
    bits: [
      { mask: 0x01, label: 'Cait Sith Sister Ray comment' },
      { mask: 0x02, label: 'Don Corneo doorman first talk' },
    ],
  },
  { offset: 196, length: 1, type: 'u8', label: 'Cloud level pre-Jenova Synthesis', description: 'Level placeholder for Jenova Synthesis boost formula' },
  { offset: 197, length: 1, type: 'u8', label: 'Barret level pre-Jenova Synthesis' },
  { offset: 198, length: 1, type: 'u8', label: 'Tifa level pre-Jenova Synthesis' },
  { offset: 199, length: 1, type: 'u8', label: 'Red XIII level pre-Jenova Synthesis' },
  { offset: 200, length: 1, type: 'u8', label: 'Yuffie level pre-Jenova Synthesis' },
  { offset: 201, length: 1, type: 'u8', label: 'Cait Sith level pre-Jenova Synthesis' },
  { offset: 202, length: 1, type: 'u8', label: 'Vincent level pre-Jenova Synthesis' },
  { offset: 203, length: 1, type: 'u8', label: 'Cid level pre-Jenova Synthesis' },
  { offset: 204, length: 1, type: 'u8', label: 'Bizarro Sephiroth group count', description: '1-3 groups for final battle configuration' },
  { offset: 205, length: 1, type: 'u8', label: 'Bizarro Sephiroth progress value', description: 'Battle progression state (0-6)' },
  {
    offset: 206,
    length: 1,
    type: 'bitmask',
    label: 'Great Glacier snowboard path',
    description: 'Path taken determines landing location',
    bits: [
      { mask: 0x01, label: 'Left both times (Forest)' },
      { mask: 0x02, label: 'Right both times (Frostbite cave)' },
      { mask: 0x04, label: 'Left-Right (Main gate)' },
      { mask: 0x08, label: 'Right-Left (Single tree)' },
    ],
  },
  { offset: 207, length: 1, type: 'u8', label: 'Yuffie regular party member', description: '0x6E: Yes, 0x6F: No (affects forest encounters)' },
  {
    offset: 208,
    length: 1,
    type: 'bitmask',
    label: 'MDS7PLR1 event flags',
    bits: [
      { mask: 0x01, label: 'Everyone runs to hideout' },
      { mask: 0x02, label: 'Pillar view call event' },
      { mask: 0x04, label: 'Barret returns and calls' },
      { mask: 0x08, label: 'After returning from pillar' },
      { mask: 0x10, label: 'Right soldier talked twice' },
    ],
  },
  {
    offset: 209,
    length: 1,
    type: 'bitmask',
    label: 'Sector 7 conversations 1',
    bits: [
      { mask: 0x01, label: 'Enter S7 bar first time' },
      { mask: 0x02, label: '7th Heaven initial scene' },
      { mask: 0x04, label: 'Tifa exits bar' },
      { mask: 0x08, label: 'Barret waits outside bar' },
      { mask: 0x10, label: 'Barret talks before bar' },
      { mask: 0x20, label: 'Enter S7 bar again' },
      { mask: 0x40, label: 'Girl talks reactor explosion' },
      { mask: 0x80, label: 'Enter S7 bar third time' },
    ],
  },
  {
    offset: 210,
    length: 1,
    type: 'bitmask',
    label: 'Sector 7 conversations 2',
    bits: [
      { mask: 0x01, label: 'Wake up in hideout 1' },
      { mask: 0x02, label: 'Wake up in hideout 2' },
      { mask: 0x10, label: 'Avalanche runs to train station' },
      { mask: 0x20, label: 'Auto-set after bit 4' },
    ],
  },
  {
    offset: 211,
    length: 1,
    type: 'bitmask',
    label: 'Sector 7 conversations 3',
    bits: [
      { mask: 0x01, label: 'Tell Tifa about Barret fight' },
      { mask: 0x04, label: 'Auto Tifa Barret fight talk' },
    ],
  },
  {
    offset: 212,
    length: 1,
    type: 'bitmask',
    label: 'Sector 7 conversations 4',
    bits: [
      { mask: 0x01, label: 'Barret changes in bar 1' },
      { mask: 0x02, label: 'Barret changes in bar 2' },
      { mask: 0x04, label: 'After talking to Tifa' },
      { mask: 0x08, label: 'Choose no drink with Tifa' },
      { mask: 0x10, label: 'Choose strong drink with Tifa' },
    ],
  },
  {
    offset: 213,
    length: 1,
    type: 'bitmask',
    label: 'Sector 7 conversations 5',
    bits: [
      { mask: 0x01, label: 'Tifa calls machine down' },
      { mask: 0x02, label: 'Elevator in hideout' },
      { mask: 0x04, label: 'Wake up in hideout 3' },
      { mask: 0x08, label: 'Wake up in hideout 4' },
      { mask: 0x10, label: 'Wake up in hideout 5' },
      { mask: 0x40, label: 'After Barret materia tutorial' },
    ],
  },
  {
    offset: 214,
    length: 1,
    type: 'bitmask',
    label: 'Sector 7 conversations 6',
    bits: [
      { mask: 0x01, label: 'After hideout first talk 1' },
      { mask: 0x02, label: 'After hideout first talk 2' },
      { mask: 0x04, label: 'Tifa enters scene' },
      { mask: 0x08, label: 'Wake up in hideout 6' },
      { mask: 0x20, label: 'Wake up in hideout 7' },
      { mask: 0x40, label: 'Wake up in hideout 8' },
      { mask: 0x80, label: 'Exit hideout talk to Tifa' },
    ],
  },
  {
    offset: 215,
    length: 1,
    type: 'bitmask',
    label: 'Sector 5 training room items',
    bits: [
      { mask: 0x10, label: 'All materia after Ether' },
      { mask: 0x20, label: 'Ether chest from ceiling' },
    ],
  },
  {
    offset: 216,
    length: 1,
    type: 'bitmask',
    label: 'MDS7ST3 event flags',
    bits: [
      { mask: 0x01, label: 'Everyone runs to hideout' },
      { mask: 0x02, label: 'Trainman war conversation' },
      { mask: 0x04, label: 'Station pair agreement' },
      { mask: 0x08, label: 'Jessie/Biggs/Wedge enter train' },
    ],
  },
  {
    offset: 223,
    length: 1,
    type: 'bitmask',
    label: 'Midgar train flags',
    bits: [
      { mask: 0x01, label: 'Talk to Biggs to Sector 7' },
      { mask: 0x02, label: 'Talk to Wedge twice' },
      { mask: 0x04, label: 'Talk to Jessie before map' },
      { mask: 0x10, label: 'ROOTMAP check flag' },
    ],
  },
  {
    offset: 236,
    length: 1,
    type: 'bitmask',
    label: 'Junon event flags',
    bits: [
      { mask: 0x04, label: 'Soldiers running after parade' },
      { mask: 0x20, label: 'Enemy Skill materia' },
    ],
  },
];

export const bank11Fields: SaveVarDefinition[] = [
  { offset: 0, length: 6, type: 'u8', label: 'Chocobo race name', description: 'Chocobo name in FF Text format' },
  { offset: 6, length: 1, type: 'u8', label: 'Chocobo race jockey', description: '00: Cloud, 01: Tifa, 02: Cid' },
  { offset: 7, length: 1, type: 'u8', label: 'Chocobo race course', description: '00: Long course, 01: Short course' },
  { offset: 8, length: 1, type: 'u8', label: 'Bet selection screen', description: '00: Enabled, 01: Disabled' },
  { offset: 9, length: 1, type: 'u8', label: 'Chocobo race unknown flag 1', description: 'Context-dependent racing flag' },
  { offset: 10, length: 2, type: 'u16', label: 'Chocobo sprint speed' },
  { offset: 12, length: 2, type: 'u16', label: 'Chocobo speed' },
  { offset: 14, length: 1, type: 'u8', label: 'Chocobo acceleration' },
  { offset: 15, length: 1, type: 'u8', label: 'Chocobo cooperation' },
  { offset: 16, length: 1, type: 'u8', label: 'Chocobo intelligence' },
  { offset: 17, length: 1, type: 'u8', label: 'Chocobo type', description: 'Yellow, Green, Blue, Black, Gold' },
  { offset: 18, length: 1, type: 'u8', label: 'Chocobo personality' },
  { offset: 19, length: 1, type: 'u8', label: 'Chocobo race unknown flag 2', description: 'Context-dependent racing flag' },
  { offset: 20, length: 1, type: 'u8', label: 'Chocobo race unknown flag 3', description: 'Always 1' },
  { offset: 21, length: 1, type: 'u8', label: 'Chocobo race unknown flag 4', description: 'Always 0' },
  { offset: 22, length: 1, type: 'u8', label: 'Joe/TEIOH challenge', description: '00: No, 01: Yes' },
  { offset: 23, length: 1, type: 'u8', label: 'Selected race rank', description: '00: Class C, 01: Class B, 02: Class A, 03: Class S' },
  { offset: 24, length: 1, type: 'u8', label: 'Chocobo race unknown flag 5', description: 'Random 0/15 or context-dependent' },
  { offset: 25, length: 1, type: 'u8', label: 'Race finish place', description: '0-5 finish position, 0xFF when not racing' },
  { offset: 26, length: 2, type: 'u16', label: 'Chocobo stamina' },
  { offset: 28, length: 1, type: 'u8', label: 'Winning prize ID', description: 'Prize received for winning chocobo race (0-23, 255=no prize)' },
  {
    offset: 104,
    length: 1,
    type: 'bitmask',
    label: 'Final battle progression flags',
    bits: [
      { mask: 0x01, label: 'Unknown flag 1' },
      { mask: 0x02, label: 'Unknown flag 2' },
      { mask: 0x04, label: 'Unknown flag 3' },
      { mask: 0x08, label: 'Final Battle' },
      { mask: 0x10, label: 'Unknown flag 4' },
      { mask: 0x20, label: 'Unknown flag 5' },
      { mask: 0x40, label: 'Unknown flag 6' },
      { mask: 0x80, label: 'Unknown flag 7' },
    ],
  },
  {
    offset: 106,
    length: 1,
    type: 'bitmask',
    label: 'Yuffie materia quest disabled members',
    description: 'Party members disabled during stolen materia quest',
    bits: [
      { mask: 0x01, label: 'Unused' },
      { mask: 0x02, label: 'Barret' },
      { mask: 0x04, label: 'Tifa' },
      { mask: 0x08, label: 'Red XIII' },
      { mask: 0x10, label: 'Cait Sith' },
      { mask: 0x20, label: 'Cid' },
      { mask: 0x40, label: 'Vincent' },
      { mask: 0x80, label: 'Aeris' },
    ],
  },
  { offset: 107, length: 2, type: 'u16', label: 'G-Bike last score' },
  { offset: 109, length: 2, type: 'u16', label: 'G-Bike high score' },
  { offset: 111, length: 1, type: 'u8', label: 'Snowboard temp variable' },
  { offset: 112, length: 4, type: 'u8', label: 'Snowboard beginner fastest time', description: 'Format: MMSSTTT0 (minutes/seconds/thousandths)' },
  { offset: 116, length: 4, type: 'u8', label: 'Snowboard expert fastest time', description: 'Format: MMSSTTT0 (minutes/seconds/thousandths)' },
  { offset: 120, length: 4, type: 'u8', label: 'Snowboard crazy fastest time', description: 'Format: MMSSTTT0 (minutes/seconds/thousandths)' },
  { offset: 124, length: 1, type: 'u8', label: 'Snowboard beginner high score' },
  { offset: 125, length: 1, type: 'u8', label: 'Snowboard expert high score' },
  { offset: 126, length: 1, type: 'u8', label: 'Snowboard crazy high score' },
  { offset: 127, length: 1, type: 'u8', label: 'Snowboard temp variable 2' },
  { offset: 128, length: 2, type: 'u16', label: 'Roller coaster 2nd rank' },
  { offset: 130, length: 2, type: 'u16', label: 'Roller coaster 3rd rank' },
  {
    offset: 132,
    length: 1,
    type: 'bitmask',
    label: 'Mythril & Chocobo Sage quest',
    bits: [
      { mask: 0x01, label: 'Talked to weapon seller' },
      { mask: 0x02, label: 'Chocobo Sage finished remembering' },
      { mask: 0x04, label: 'Unknown flag' },
      { mask: 0x08, label: 'Large Materia advice' },
      { mask: 0x10, label: 'Mythril given to weapon seller' },
      { mask: 0x20, label: 'Asked Sage about Chocobo' },
      { mask: 0x40, label: 'Sage remembered something new' },
      { mask: 0x80, label: 'First talk to Chole' },
    ],
  },
  { offset: 133, length: 1, type: 'u8', label: 'Chocobo Sage progression', description: '01: First talk, 02-04: Blue/Green, 05: Black, 06-08: Gold, 09-10: Zeio Nuts' },
  { offset: 134, length: 2, type: 'u16', label: 'Chocobo breeding tutorial battles' },
  { offset: 136, length: 1, type: 'u8', label: 'Chocobo Sage random battles part' },
  {
    offset: 138,
    length: 1,
    type: 'bitmask',
    label: 'Chocobo race progression flags',
    bits: [
      { mask: 0x01, label: 'First talk to Ester' },
      { mask: 0x02, label: 'Race engine launched' },
      { mask: 0x04, label: 'First time racing' },
      { mask: 0x08, label: 'Beat Mog House' },
      { mask: 0x10, label: 'Won 9 races for Rank S' },
      { mask: 0x20, label: 'Won 19 races for prizes' },
      { mask: 0x40, label: 'Unknown flag' },
      { mask: 0x80, label: 'Unknown flag' },
    ],
  },
  { offset: 139, length: 1, type: 'u8', label: 'Selected chocobo stable position', description: '0-5 stable slot number' },
  {
    offset: 140,
    length: 1,
    type: 'bitmask',
    label: 'Northern Cave received items',
    description: 'Items received from party members',
    bits: [
      { mask: 0x01, label: 'Barret' },
      { mask: 0x02, label: 'Tifa' },
      { mask: 0x04, label: 'Red XIII' },
      { mask: 0x08, label: 'Cid' },
      { mask: 0x10, label: 'Cait Sith' },
      { mask: 0x20, label: 'Yuffie' },
      { mask: 0x40, label: 'Vincent' },
      { mask: 0x80, label: 'Unknown flag' },
    ],
  },
  { offset: 143, length: 1, type: 'u8', label: "Lucrecia's Cave progression" },
  { offset: 145, length: 2, type: 'u16', label: "Lucrecia's Cave battles count", description: 'Battles needed for Chaos & Death Penalty' },
  { offset: 149, length: 2, type: 'u16', label: 'Roller coaster 1st rank' },
  { offset: 184, length: 2, type: 'u16', label: 'Northern Cave save crystal field' },
  { offset: 186, length: 6, type: 'u8', label: 'Northern Cave save crystal position', description: 'X, Y, Z coordinates' },
];

export const bank13Fields: SaveVarDefinition[] = [
  { offset: 0, length: 1, type: 'u8', label: 'Required game disc' },
  {
    offset: 1,
    length: 1,
    type: 'bitmask',
    label: "Tifa's house flags",
    bits: [
      { mask: 0x01, label: 'Final Heaven' },
      { mask: 0x02, label: 'Cloud played piano (flashback)' },
      { mask: 0x04, label: 'Elemental Materia' },
    ],
  },
  { offset: 2, length: 1, type: 'u8', label: 'Bombing mission start flag', description: '0x14: Yes, 0x56: No' },
  { offset: 3, length: 1, type: 'u8', label: 'Northern Cave progress' },
  { offset: 6, length: 2, type: 'u16', label: 'Great Glacier step counter', description: 'Steps until passing out (544 max)' },
  { offset: 30, length: 1, type: 'u8', label: 'Field pointer mask', description: '0x00: Inactive, 0x02: Active (arrows over party leader)' },
  { offset: 68, length: 2, type: 'u16', label: 'Chocobo 1 stamina' },
  { offset: 70, length: 2, type: 'u16', label: 'Chocobo 2 stamina' },
  { offset: 72, length: 2, type: 'u16', label: 'Chocobo 3 stamina' },
  { offset: 74, length: 2, type: 'u16', label: 'Chocobo 4 stamina' },
  { offset: 76, length: 2, type: 'u16', label: 'Chocobo 5 stamina' },
  { offset: 78, length: 2, type: 'u16', label: 'Chocobo 6 stamina' },
  {
    offset: 80,
    length: 1,
    type: 'bitmask',
    label: 'Vincent & submarine flags',
    bits: [
      { mask: 0x01, label: 'Unknown flag 1' },
      { mask: 0x02, label: 'Unknown flag 2' },
      { mask: 0x04, label: 'Vincent regularly' },
      { mask: 0x08, label: 'Gray submarine' },
      { mask: 0x10, label: 'Unknown flag 3' },
      { mask: 0x20, label: 'Unknown flag 4' },
      { mask: 0x40, label: 'Unknown flag 5' },
      { mask: 0x80, label: 'Unknown flag 6' },
    ],
  },
  {
    offset: 82,
    length: 1,
    type: 'bitmask',
    label: 'Vehicle submarine flags',
    bits: [
      { mask: 0x01, label: 'Unknown flag 1' },
      { mask: 0x02, label: 'Unknown flag 2' },
      { mask: 0x04, label: 'Red submarine' },
      { mask: 0x08, label: 'Unknown flag 3' },
      { mask: 0x10, label: 'Unknown flag 4' },
      { mask: 0x20, label: 'Unknown flag 5' },
      { mask: 0x40, label: 'Unknown flag 6' },
      { mask: 0x80, label: 'Unknown flag 7' },
    ],
  },
  { offset: 89, length: 1, type: 'u8', label: 'Northern Cave Yuffie split path' },
  { offset: 91, length: 1, type: 'u8', label: 'Save flag', description: '0x02: In save menu' },
  { offset: 96, length: 1, type: 'u8', label: 'Northern Cave progress 1' },
  { offset: 97, length: 1, type: 'u8', label: 'Northern Cave progress 2' },
  { offset: 104, length: 24, type: 'u8', label: 'Location name', description: 'FF Text format' },
  { offset: 133, length: 1, type: 'u8', label: 'World map tutorial seen', description: '0x3B: Seen, 0x33: Not seen' },
  {
    offset: 134,
    length: 1,
    type: 'bitmask',
    label: 'Submarine tutorial flags',
    bits: [
      { mask: 0x01, label: 'Unknown flag 1' },
      { mask: 0x02, label: 'Unknown flag 2' },
      { mask: 0x04, label: 'Red submarine tutorial' },
      { mask: 0x08, label: 'Unknown flag 3' },
      { mask: 0x10, label: 'Unknown flag 4' },
      { mask: 0x20, label: 'Unknown flag 5' },
      { mask: 0x40, label: 'Unknown flag 6' },
      { mask: 0x80, label: 'Unknown flag 7' },
    ],
  },
  { offset: 135, length: 1, type: 'u8', label: 'Ruby Weapon form', description: 'bit 0: Small (before encounter), bit 1: Big (after encounter)' },
  { offset: 150, length: 1, type: 'u8', label: 'Random number', description: '0-2, set when changing field maps' },
  { offset: 184, length: 3, type: 'u24', label: 'Party leader X coordinate', description: 'World map position (0-295000)' },
  { offset: 187, length: 1, type: 'u8', label: 'Party leader direction', description: 'Viewing angle (0-255 for 0-359°)' },
  { offset: 188, length: 3, type: 'u24', label: 'Party leader Y coordinate', description: 'World map position (1-230000)' },
  { offset: 191, length: 1, type: 'u8', label: 'Party leader Z altitude', description: 'Height (-128 to 127, 0=sea level)' },
  { offset: 192, length: 8, type: 'u8', label: 'Wild chocobo coordinates' },
  { offset: 200, length: 8, type: 'u8', label: 'Tiny Bronco/Chocobo coordinates' },
  { offset: 208, length: 8, type: 'u8', label: 'Buggy/Highwind coordinates' },
  { offset: 216, length: 8, type: 'u8', label: 'Submarine coordinates' },
  { offset: 224, length: 8, type: 'u8', label: 'Weapon boss coordinates', description: 'Diamond/Ultimate/Ruby Weapon positions' },
  { offset: 232, length: 2, type: 'u16', label: '1st snow pole X coordinate' },
  { offset: 234, length: 2, type: 'u16', label: '1st snow pole Y coordinate' },
  { offset: 236, length: 2, type: 'u16', label: '2nd snow pole X coordinate' },
  { offset: 238, length: 2, type: 'u16', label: '2nd snow pole Y coordinate' },
  { offset: 240, length: 2, type: 'u16', label: '3rd snow pole X coordinate' },
  { offset: 242, length: 2, type: 'u16', label: '3rd snow pole Y coordinate' },
  { offset: 248, length: 2, type: 'u16', label: 'Camera angle & rotation', description: 'World map camera position' },
  { offset: 251, length: 2, type: 'u16', label: 'Snow pole number', description: 'Current pole index (0-2, cycling)' },
  { offset: 252, length: 1, type: 'u8', label: 'Wild chocobo type', description: '0: Not displayed, 1: Wonderful, 2: Great, 3: Good, 4: Fair, 5: Average, 6: Poor, 7: Bad, 8: Terrible' },
  { offset: 253, length: 1, type: 'u8', label: 'Current vehicle', description: '0: On foot, 3: Highwind, 4: Wild chocobo, 13: Submarine, 19: Stable chocobo' },
];

export const bank7Fields: SaveVarDefinition[] = [
  {
    offset: 2,
    length: 1,
    type: 'bitmask',
    label: 'World map camera & display',
    description: 'Camera: Aerial(00)/Closeup(20), Map: Off(80)/Small(00)/Large(40)',
    bits: [
      { mask: 0x20, label: 'Closeup camera' },
      { mask: 0x40, label: 'Large map' },
      { mask: 0x80, label: 'Map off' },
    ],
  },
  {
    offset: 32,
    length: 2,
    type: 'bitmask',
    label: 'Field items mask',
    bits: [
      { mask: 0x0001, label: 'First potion MD1STIN' },
      { mask: 0x0002, label: 'Second potion MD1STIN' },
      { mask: 0x0004, label: 'Potion NMKIN3' },
      { mask: 0x0008, label: 'Phoenix Down NKMIN1' },
    ],
  },
  {
    offset: 34,
    length: 1,
    type: 'bitmask',
    label: 'Chocobo Farm items mask',
    bits: [
      { mask: 0x01, label: 'Destruct materia animation displayed' },
      { mask: 0x02, label: 'Destruct materia' },
      { mask: 0x04, label: 'Enemy Skill materia' },
      { mask: 0x08, label: 'Enemy Skill animation displayed' },
      { mask: 0x10, label: 'Odin materia' },
      { mask: 0x20, label: 'Odin animation displayed' },
      { mask: 0x40, label: 'Counter materia' },
      { mask: 0x80, label: 'Magic Plus materia' },
    ],
  },
  { offset: 77, length: 1, type: 'u8', label: 'Buggy vehicle status', description: '0x0E: On, 0x0C: Off' },
  {
    offset: 80,
    length: 1,
    type: 'bitmask',
    label: 'Mythril Mine items mask',
    bits: [
      { mask: 0x01, label: 'Tent 4sbwy_6' },
      { mask: 0x02, label: 'Potion 4sbwy_3' },
      { mask: 0x04, label: 'Ether 4sbwy_1' },
      { mask: 0x08, label: 'Ether psdun_3' },
      { mask: 0x10, label: 'Hi-Potion psdun_4' },
      { mask: 0x20, label: 'Elixir psdun_4' },
      { mask: 0x40, label: 'Long Range materia' },
      { mask: 0x80, label: 'Titan materia' },
    ],
  },
  {
    offset: 81,
    length: 1,
    type: 'bitmask',
    label: 'Various location items mask',
    bits: [
      { mask: 0x01, label: 'Ether elmin2_2' },
      { mask: 0x02, label: 'Comet materia losin1' },
      { mask: 0x04, label: 'Deathblow materia gonjun1' },
      { mask: 0x08, label: 'Hades materia q_4' },
      { mask: 0x10, label: 'Outsider q_4' },
      { mask: 0x20, label: 'Escourt Guard q_3' },
      { mask: 0x40, label: 'Conformer q_3' },
      { mask: 0x80, label: 'Spirit Lance q_4' },
    ],
  },
  {
    offset: 82,
    length: 1,
    type: 'bitmask',
    label: 'Various location items mask 2',
    bits: [
      { mask: 0x01, label: 'Heaven\'s Cloud q_1' },
      { mask: 0x02, label: 'Megalixir q_3' },
      { mask: 0x04, label: 'Megalixir q_4' },
      { mask: 0x08, label: 'Elixir losinn' },
      { mask: 0x10, label: 'Guard Source losin2' },
      { mask: 0x20, label: 'Magic Source losin3' },
      { mask: 0x40, label: 'Elixir las1_2/las4_0' },
      { mask: 0x80, label: 'Mystle las1_2/las4_0' },
    ],
  },
  {
    offset: 85,
    length: 1,
    type: 'bitmask',
    label: 'Kalm items mask 1',
    bits: [
      { mask: 0x01, label: 'Hidden Ether second floor' },
    ],
  },
  {
    offset: 87,
    length: 1,
    type: 'bitmask',
    label: 'Kalm Traveler rewards visibility',
    bits: [
      { mask: 0x01, label: 'Guide Book' },
      { mask: 0x02, label: 'Master Command' },
      { mask: 0x04, label: 'Master Magic' },
      { mask: 0x08, label: 'Master Summon' },
      { mask: 0x10, label: 'Gold Chocobo' },
    ],
  },
  {
    offset: 88,
    length: 1,
    type: 'bitmask',
    label: 'Kalm items mask 2',
    bits: [
      { mask: 0x01, label: 'Peacemaker in house' },
    ],
  },
  {
    offset: 89,
    length: 1,
    type: 'bitmask',
    label: 'Kalm items mask 3',
    bits: [
      { mask: 0x02, label: 'Hidden Ether house next to Inn' },
      { mask: 0x08, label: 'Guard Source' },
      { mask: 0x10, label: 'Hidden Ether' },
    ],
  },
  {
    offset: 90,
    length: 1,
    type: 'bitmask',
    label: 'Mythril Mine items mask 2',
    bits: [
      { mask: 0x10, label: 'Mind Source' },
      { mask: 0x20, label: 'Tent' },
    ],
  },
  { offset: 96, length: 1, type: 'u8', label: 'Bizarro Group 1 member 1', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 97, length: 1, type: 'u8', label: 'Bizarro Group 1 member 2', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 98, length: 1, type: 'u8', label: 'Bizarro Group 1 member 3', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 99, length: 1, type: 'u8', label: 'Bizarro Group 2 member 1', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 100, length: 1, type: 'u8', label: 'Bizarro Group 2 member 2', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 101, length: 1, type: 'u8', label: 'Bizarro Group 2 member 3', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 102, length: 1, type: 'u8', label: 'Bizarro Group 3 member 1', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 103, length: 1, type: 'u8', label: 'Bizarro Group 3 member 2', description: 'Party member char ID for Bizarro Sephiroth battle' },
  { offset: 104, length: 1, type: 'u8', label: 'Bizarro Group 3 member 3', description: 'Party member char ID for Bizarro Sephiroth battle' },
  {
    offset: 122,
    length: 1,
    type: 'bitmask',
    label: 'Junon items mask',
    bits: [
      { mask: 0x01, label: 'Mind Source' },
      { mask: 0x02, label: 'Power Source' },
      { mask: 0x04, label: 'Guard Source' },
      { mask: 0x10, label: '1/35 Soldier' },
      { mask: 0x20, label: 'Luck Source' },
    ],
  },
  { offset: 140, length: 1, type: 'u8', label: 'Field screen rain switch', description: 'Non-zero to turn on rain effect' },
];

export const banks: Record<number, SaveVarDefinition[]> = {
  1: bank1Fields,
  2: bank3Fields,
  3: bank11Fields,
  4: bank13Fields,
  5: bank7Fields,
};
